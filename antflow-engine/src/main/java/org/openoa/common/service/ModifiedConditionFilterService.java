package org.openoa.common.service;

import com.alibaba.fastjson2.JSON;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.openoa.base.constant.enums.NodePropertyEnum;
import org.openoa.base.constant.enums.NodeTypeEnum;
import org.openoa.base.interf.ConditionService;
import org.openoa.base.util.BpmnUtils;
import org.openoa.base.vo.*;
import org.openoa.base.exception.JiMuBizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 0.5
 */
@Component
@Slf4j
public class ModifiedConditionFilterService {

    @Autowired
    private ConditionService conditionService;

    /**
     * filter nodes by conditions
     * @param bpmnConfVo
     * @param bpmnStartConditionsVo
     * @return
     */
    public void conditionfilterNode(BpmnConfVo bpmnConfVo, BpmnStartConditionsVo bpmnStartConditionsVo, boolean isOnlyGetFollowNodes) {
        List<BpmnNodeVo> nodeList = bpmnConfVo.getNodes();
        if (CollectionUtils.isEmpty(nodeList)) {
            log.warn("bpmn conf has no nodes");
            return ;
        }
        Map<String, BpmnNodeVo> nodeIdMapNode = new HashMap<>(16);
        BpmnNodeVo startNode = getNodeMapAndStartNode(nodeList, nodeIdMapNode);
        if (startNode == null) {
            log.info("process has no start user,bpmnCode:{}", bpmnConfVo.getBpmnCode());
            throw new JiMuBizException("999", "process has no start user");
        }
        //filter by conditions
        List<BpmnNodeVo> filterNodes = filterNode(startNode, nodeIdMapNode, bpmnStartConditionsVo, isOnlyGetFollowNodes);
        bpmnConfVo.setNodes(filterNodes);
    }

    /**
     * turn node from list to map and return the start node
     *
     * @param nodeList
     * @param nodeIdMapNode
     * @return
     */
    private BpmnNodeVo getNodeMapAndStartNode(List<BpmnNodeVo> nodeList, Map<String, BpmnNodeVo> nodeIdMapNode) {
        BpmnNodeVo startNode = null;
        for (BpmnNodeVo bpmnNodeVo : nodeList) {
            nodeIdMapNode.put(bpmnNodeVo.getNodeId(), bpmnNodeVo);
            if (NodeTypeEnum.NODE_TYPE_START.getCode().equals(bpmnNodeVo.getNodeType())) {
                if (startNode == null) {
                    startNode = bpmnNodeVo;
                } else {
                    log.info("multiple start user,nodeId:{}", bpmnNodeVo.getNodeId());
                    throw new JiMuBizException("999", "process has multiple start user");
                }
            }
        }
        return startNode;
    }

    /**
     * filter nodes by conditions
     *
     * @param startNode
     * @param nodeIdMapNode
     * @param bpmnStartConditionsVo
     * @return
     */
    private List<BpmnNodeVo> filterNode(BpmnNodeVo startNode, Map<String, BpmnNodeVo> nodeIdMapNode,
                                        BpmnStartConditionsVo bpmnStartConditionsVo,
                                        boolean isOnlyGetFollowNodes) {
        List<BpmnNodeVo> nodeList = new ArrayList<>();
        BpmnNodeParamsVo params = new BpmnNodeParamsVo();
        params.setNodeTo(startNode.getNodeTo().get(0));
        startNode.setParams(params);
        String nextId = params.getNodeTo();
        do {
            if (NodeTypeEnum.NODE_TYPE_GATEWAY.getCode().equals(startNode.getNodeType())&&Boolean.TRUE.equals(startNode.getIsParallel())){
                parallelTreate(startNode,nodeIdMapNode,nodeList,bpmnStartConditionsVo, isOnlyGetFollowNodes);
            }
            if(NodeTypeEnum.NODE_TYPE_PARALLEL_GATEWAY.getCode().equals(startNode.getNodeType())){
                BpmnNodeVo aggregationNode = BpmnUtils.getAggregationNode(startNode, nodeIdMapNode.values());
                treatParallelGateWayRecursively(startNode,aggregationNode,nodeIdMapNode,nodeList,bpmnStartConditionsVo,isOnlyGetFollowNodes);
                nextId=aggregationNode.getNodeId();
            }else{
                recursionTreate(startNode, nodeIdMapNode, nodeList, bpmnStartConditionsVo, isOnlyGetFollowNodes);
                nextId=startNode.getParams().getNodeTo();
            }
            startNode=nodeIdMapNode.get(nextId);
        }while (!Strings.isNullOrEmpty(nextId));

        List<BpmnNodeVo> list = deleteConditionNode(nodeList);
        //check finally nodes
        checkNode(list);
        return list;
    }

    private void treatParallelGateWayRecursively(BpmnNodeVo outerMostParallelGatewayNode,
                                                 BpmnNodeVo itsAggregationNode,
                                                 Map<String, BpmnNodeVo> nodeIdMapNode,
                                                 List<BpmnNodeVo> nodeList,
                                                 BpmnStartConditionsVo bpmnStartConditionsVo,
                                                 boolean isOnlyGetFollowNodes){
        if (itsAggregationNode == null) {
            throw new JiMuBizException("there is a parallel gateway node,but can not get its aggregation node!");
        }
        recursionTreate(outerMostParallelGatewayNode,nodeIdMapNode,nodeList,bpmnStartConditionsVo, isOnlyGetFollowNodes);
        String aggregationNodeNodeId = itsAggregationNode.getNodeId();
        List<String> nodeTos = outerMostParallelGatewayNode.getNodeTo();
        for (String nodeTo : nodeTos) {
            BpmnNodeVo currentNodeVo = nodeIdMapNode.get(nodeTo);
            //treat all nodes between parallel gateway and its aggregation node(not include the latter)
            for (BpmnNodeVo nodeVo = currentNodeVo; nodeVo!=null&&!nodeVo.getNodeId().equals(aggregationNodeNodeId); nodeVo = nodeIdMapNode.get(nodeVo.getParams().getNodeTo())) {
                if (NodeTypeEnum.NODE_TYPE_PARALLEL_GATEWAY.getCode().equals(nodeVo.getNodeType())) {
                    BpmnNodeVo aggregationNode = BpmnUtils.getAggregationNode(nodeVo, nodeIdMapNode.values());
                    treatParallelGateWayRecursively(nodeVo, aggregationNode,nodeIdMapNode,nodeList,bpmnStartConditionsVo, isOnlyGetFollowNodes);
                }else{
                    recursionTreate(nodeVo,nodeIdMapNode,nodeList,bpmnStartConditionsVo, isOnlyGetFollowNodes);
                }

            }
        }
    }
    /**
     * verify node
     *
     * @param list
     */
    private void checkNode(List<BpmnNodeVo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        int nodeCustomize = 0;
        for (BpmnNodeVo bpmnNodeVo : list) {
            if (NodeTypeEnum.NODE_TYPE_APPROVER.getCode().equals(bpmnNodeVo.getNodeType())
                    && NodePropertyEnum.NODE_PROPERTY_CUSTOMIZE.getCode().equals(bpmnNodeVo.getNodeProperty())) {
                nodeCustomize = nodeCustomize + 1;
                /*if (nodeCustomize > 1) {
                    throw new JiMuBizException("self chose module is greater than 1");
                }*/
            }
        }
    }

    /**
     * process nodes recursively
     *
     * @param node           当前节点
     * @param nodeIdMapNode
     * @param filterNodeList
     */
    private void recursionTreate(BpmnNodeVo node, Map<String, BpmnNodeVo> nodeIdMapNode,
                                 List<BpmnNodeVo> filterNodeList,
                                 BpmnStartConditionsVo bpmnStartConditionsVo,
                                 boolean isOnlyGetFollowNodes) {
        // 判断当前节点是否还有下一个节点
        if (CollectionUtils.isEmpty(node.getNodeTo())) {
            node.setParams(BpmnNodeParamsVo.builder().build());
            filterNodeList.add(node);
            return;
        }
        //to prevent from endless recursion
        if (filterNodeList.size() > nodeIdMapNode.size()) {
            log.info("nodeId error,nodeMap:{}", JSON.toJSONString(nodeIdMapNode));
            throw new JiMuBizException("999", "Node's nodeId config error");
        }
        BpmnNodeVo nextNode = null;
        BpmnNodeParamsVo params = new BpmnNodeParamsVo();

        if (!NodeTypeEnum.NODE_TYPE_GATEWAY.getCode().equals(node.getNodeType())) {
            BpmnNodeVo next = nodeIdMapNode.get(node.getNodeTo().get(0));
            if (next == null) {
                log.error("can not find out process's next node,nodeId:{},nextNodeId:{}", node.getNodeId(), node.getNodeTo().get(0));
                throw new JiMuBizException("999", "can not find out process's next node");
            }
            params.setNodeTo(CollectionUtils.isEmpty(node.getNodeTo()) ? null : node.getNodeTo().get(0));
            node.setParams(params);
            filterNodeList.add(node);
            nextNode = next;
        } else {
            List<BpmnNodeVo> beforeFilterNodeList = new ArrayList<>();
            node.getNodeTo().forEach(o -> beforeFilterNodeList.add(nodeIdMapNode.get(o)));
            if (beforeFilterNodeList.size() != node.getNodeTo().size()) {
                log.info("wrong number of conditions node,nodeId:{}", node.getNodeId());
                throw new JiMuBizException("999", "wrong number of conditions node,nodeId");
            }
            boolean isDynamicConditionGateway=Boolean.TRUE.equals(node.getIsDynamicCondition());
            //determine next node by condition
            BpmnNodeVo afterFilterNode = filterCondition(beforeFilterNodeList, bpmnStartConditionsVo,isDynamicConditionGateway);
            if (afterFilterNode == null) {
                // only get follow nodes
                if(isOnlyGetFollowNodes){
                    params.setNodeTo(null);
                    node.setParams(params);
                    filterNodeList.add(node);
                    return;
                }
                throw new JiMuBizException("999", "had no branch that meet the given condition,please contat the Administrator！");
            }
            params.setNodeTo(afterFilterNode.getNodeId());
            node.setParams(params);
            filterNodeList.add(node);
            nextNode = afterFilterNode;
        }
        //recursionTreate(nextNode, nodeIdMapNode, filterNodeList, bpmnStartConditionsVo);
    }

    private void parallelTreate(BpmnNodeVo node, Map<String, BpmnNodeVo> nodeIdMapNode,
                                List<BpmnNodeVo> filterNodeList, BpmnStartConditionsVo bpmnStartConditionsVo,
                                boolean isOnlyGetFollowNodes){


        List<BpmnNodeVo> beforeFilterNodeList = new ArrayList<>();
        node.getNodeTo().forEach(o -> beforeFilterNodeList.add(nodeIdMapNode.get(o)));
        if (beforeFilterNodeList.size() != node.getNodeTo().size()) {
            log.info("wrong number of conditions node,nodeId:{}", node.getNodeId());
            throw new JiMuBizException("999", "wrong number of conditions node,nodeId");
        }
        //determine next node by condition
        List<BpmnNodeVo> afterFilterNodes = filterParallelCondition(beforeFilterNodeList, bpmnStartConditionsVo);
        if (CollectionUtils.isEmpty(afterFilterNodes)) {
            if(isOnlyGetFollowNodes){
                node.setNodeTo(null);
                node.setNodeType(NodeTypeEnum.NODE_TYPE_PARALLEL_GATEWAY.getCode());
                return;
            }
            throw new JiMuBizException("999", "had no branch that meet the given condition,please contat the Administrator！");
        }
        List<String>nodetos=new ArrayList<>();
        for (BpmnNodeVo afterFilterNode : afterFilterNodes) {
            List<String> nodeTo = afterFilterNode.getNodeTo();
            if(!CollectionUtils.isEmpty(nodeTo)){
                nodetos.add(nodeTo.get(0));
            }
        }
        node.setNodeTo(nodetos);
        node.setNodeType(NodeTypeEnum.NODE_TYPE_PARALLEL_GATEWAY.getCode());
    }

    /**
     * filter out a node that meet the condition(if more than one,choose the one has a highest priority)
     * @param beforeFilterNodeList  nodeList
     * @param bpmnStartConditionsVo start conditions
     * @return
     */
    private BpmnNodeVo filterCondition(List<BpmnNodeVo> beforeFilterNodeList, BpmnStartConditionsVo bpmnStartConditionsVo,boolean isDynamicConditionGateway) {

        if (CollectionUtils.isEmpty(beforeFilterNodeList)) {
            log.info("condition nodes are empty");
            return null;
        }

        //get a list of condition nodes,not include defaults ones
        List<BpmnNodeVo> filterNodeList = beforeFilterNodeList
                .stream()
                .filter(o -> o.getProperty().getConditionsConf().getIsDefault() == 0 && !ObjectUtils.isEmpty(o.getProperty().getConditionsConf().getSort()))
                .sorted(Comparator.comparing(o -> ((BpmnNodeVo) o).getProperty().getConditionsConf().getSort()))
                .collect(Collectors.toList());

        //iterate the nodes to check whether it meets all the given conditions
        for (BpmnNodeVo bpmnNodeVo : filterNodeList) {
            if (!NodeTypeEnum.NODE_TYPE_CONDITIONS.getCode().equals(bpmnNodeVo.getNodeType())) {

                log.info("gateway's next node,but not a condition node,continue,nodeId:{},nodeType:{}", bpmnNodeVo.getNodeId(), bpmnNodeVo.getNodeType());
                continue;
            }

            //check whether meet the given condition
            boolean matchCondition = conditionService.checkMatchCondition(bpmnNodeVo, bpmnNodeVo.getProperty().getConditionsConf(), bpmnStartConditionsVo,isDynamicConditionGateway);

            if (!matchCondition) {
                continue;
            }
            //if there are multiple nodes that meet the conditions,only the first one will return,sometimes,it is problematic
            //there should be a way to check only one condition branch is meet specified conditions
            return bpmnNodeVo;
        }
        //there should also be a way to check only one default branch
        //has no node that meet the given conditions,then choose the default one
        List<BpmnNodeVo> defaultConditionNodes = beforeFilterNodeList
                .stream()
                .filter(o -> o.getProperty().getConditionsConf().getIsDefault() == 1)
                .collect(Collectors.toList());
        if (!ObjectUtils.isEmpty(defaultConditionNodes)) {
            return defaultConditionNodes.get(0);
        }

        //the default behavior,checked all conditions through,but can not get one branch,an error should be thrown in outside method
        return null;
    }
    private List<BpmnNodeVo> filterParallelCondition(List<BpmnNodeVo> beforeFilterNodeList, BpmnStartConditionsVo bpmnStartConditionsVo) {

        if (CollectionUtils.isEmpty(beforeFilterNodeList)) {
            log.info("condition nodes are empty");
            return null;
        }

        //get a list of condition nodes,not include defaults ones
        List<BpmnNodeVo> filterNodeList = beforeFilterNodeList
                .stream()
                .filter(o -> o.getProperty().getConditionsConf().getIsDefault() == 0 && !ObjectUtils.isEmpty(o.getProperty().getConditionsConf().getSort()))
                .sorted(Comparator.comparing(o -> ((BpmnNodeVo) o).getProperty().getConditionsConf().getSort()))
                .collect(Collectors.toList());

        List<BpmnNodeVo> filteredNodes=new ArrayList<>();
        //iterate the nodes to check whether it meets all the given conditions
        for (BpmnNodeVo bpmnNodeVo : filterNodeList) {
            if (!NodeTypeEnum.NODE_TYPE_CONDITIONS.getCode().equals(bpmnNodeVo.getNodeType())) {

                log.info("gateway's next node,but not a condition node,continue,nodeId:{},nodeType:{}", bpmnNodeVo.getNodeId(), bpmnNodeVo.getNodeType());
                continue;
            }

            //check whether meet the given condition
            boolean matchCondition = conditionService.checkMatchCondition(bpmnNodeVo, bpmnNodeVo.getProperty().getConditionsConf(), bpmnStartConditionsVo,false);

            if (!matchCondition) {
                continue;
            }
            //if there are multiple nodes that meet the conditions,only the first one will return,sometimes,it is problematic
            //there should be a way to check only one condition branch is meet specified conditions
            filteredNodes.add(bpmnNodeVo);
        }
        if(!CollectionUtils.isEmpty(filteredNodes)){
            return filteredNodes;
        }
        //there should also be a way to check only one default branch
        //has no node that meet the given conditions,then choose the default one
        List<BpmnNodeVo> defaultConditionNodes = beforeFilterNodeList
                .stream()
                .filter(o -> o.getProperty().getConditionsConf().getIsDefault() == 1)
                .collect(Collectors.toList());
        if (!ObjectUtils.isEmpty(defaultConditionNodes)) {
            return defaultConditionNodes;
        }

        //the default behavior,checked all conditions through,but can not get one branch,an error should be thrown in outside method
        return null;
    }

    private List<BpmnNodeVo> deleteConditionNode(List<BpmnNodeVo> nodeList) {
        Map<String, BpmnNodeVo> notConditionNodeMap = new LinkedHashMap<>();
        Map<String, BpmnNodeVo> conditionNodeMap = new HashMap<>(16);
        nodeList.forEach(o -> {
            if (NodeTypeEnum.NODE_TYPE_GATEWAY.getCode().equals(o.getNodeType())
                    || NodeTypeEnum.NODE_TYPE_CONDITIONS.getCode().equals(o.getNodeType())) {
                conditionNodeMap.put(o.getNodeId(), o);
            } else {
                notConditionNodeMap.put(o.getNodeId(), o);
            }
        });
        List<BpmnNodeVo> resultList = new ArrayList<>();
        for (Map.Entry<String, BpmnNodeVo> entry : notConditionNodeMap.entrySet()) {
            String nextNodeId = entry.getValue().getParams().getNodeTo();
            if (StringUtils.isBlank(nextNodeId)) {
                resultList.add(entry.getValue());
                continue;
            }

            //next node is condition node or assignee ndoe
            if (!notConditionNodeMap.containsKey(nextNodeId)) {
                String resultNodeId = findNext(nextNodeId, new ArrayList<>(), notConditionNodeMap, conditionNodeMap);
                if (StringUtils.isNotBlank(resultNodeId)) {
                    BpmnNodeVo bpmnNodeVo = notConditionNodeMap.get(resultNodeId);
                    if(!StringUtils.isEmpty(bpmnNodeVo.getNodeFrom())&&notConditionNodeMap.get(bpmnNodeVo.getNodeFrom())!=null&&
                            !NodeTypeEnum.NODE_TYPE_PARALLEL_GATEWAY.getCode().equals(notConditionNodeMap.get(bpmnNodeVo.getNodeFrom()).getNodeType())){
                        bpmnNodeVo.setNodeFrom(entry.getValue().getNodeId());
                    }

                }
                entry.getValue().setNodeTo(Lists.newArrayList(resultNodeId));
                entry.getValue().getParams().setNodeTo(resultNodeId);
            }
            resultList.add(entry.getValue());
        }
        return resultList;
    }

    /**
     * a node that is not a condition node's next none condition node
     *
     * @param nodeId
     * @param addNodeIdList
     * @param notConditionNodeMap
     * @param conditionNodeMap
     * @return
     */
    private String findNext(String nodeId, List<String> addNodeIdList, Map<String, BpmnNodeVo> notConditionNodeMap, Map<String, BpmnNodeVo> conditionNodeMap) {
        if (notConditionNodeMap.containsKey(nodeId) || StringUtils.isBlank(nodeId)) {
            return nodeId;
        }
        //solve endless recursion
        if (addNodeIdList.contains(nodeId)) {
            log.info("node id forms a cycle,nodeId:{}", nodeId);
            throw new JiMuBizException("999", "node id forms a cycle");
        }
        BpmnNodeVo bpmnNodeVo = conditionNodeMap.get(nodeId);
        if (bpmnNodeVo == null) {
            log.info("has no node id,nodeId:{}", nodeId);
            throw new JiMuBizException("999", "has no node id");
        }
        addNodeIdList.add(nodeId);
        return findNext(bpmnNodeVo.getParams().getNodeTo(), addNodeIdList, notConditionNodeMap, conditionNodeMap);
    }
}