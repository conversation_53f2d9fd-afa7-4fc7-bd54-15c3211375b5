package org.openoa.engine.bpmnconf.adp.conditionfilter.conditionjudge;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.openoa.base.constant.enums.CustomFieldEnum;
import org.openoa.base.exception.JiMuBizException;
import org.openoa.base.service.SyncEhrOrgInfoService;
import org.openoa.base.service.TUserRoleService;
import org.openoa.base.vo.BpmnNodeConditionsConfBaseVo;
import org.openoa.base.vo.BpmnStartConditionsVo;
import org.openoa.base.vo.OrgEmpTagInfoVo;
import org.openoa.engine.bpmnconf.service.FourPredict;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class LFStringConditionJudge extends AbstractLFConditionJudge {

    @Resource
    private SyncEhrOrgInfoService syncEhrOrgInfoService;

    @Resource
    private TUserRoleService tUserRoleService;

    @Override
    public boolean judge(String nodeId, BpmnNodeConditionsConfBaseVo conditionsConf, BpmnStartConditionsVo bpmnStartConditionsVo, int index) {
        return this.strJudge(conditionsConf, bpmnStartConditionsVo, (f, a, b, c) -> {
            if (CustomFieldEnum.isCustomerField(f.toString())) {
                // 库中获取的是字符串
                List<OrgEmpTagInfoVo> dBOrgEmpTagInfoVos = JSON.parseArray(a.toString(), OrgEmpTagInfoVo.class);
                List<OrgEmpTagInfoVo> userOrgEmpTagInfoVo = JSON.parseArray(b.toString(), OrgEmpTagInfoVo.class);
                return isAllMatch(userOrgEmpTagInfoVo, dBOrgEmpTagInfoVos);
            } else {
                return a.toString().equalsIgnoreCase(b.toString());
            }
        }, index);
    }


    private boolean isAllMatch(List<OrgEmpTagInfoVo> userOrgEmpTagInfoVos, List<OrgEmpTagInfoVo> dBOrgEmpTagInfoVos) {
        if (CollectionUtils.isEmpty(userOrgEmpTagInfoVos) || CollectionUtils.isEmpty(dBOrgEmpTagInfoVos)) {
            return false;
        }

        Map<Integer, List<OrgEmpTagInfoVo>> conditionGroup = dBOrgEmpTagInfoVos.stream()
                .collect(Collectors.groupingBy(OrgEmpTagInfoVo::getControlDataType, Collectors.toList()));

        // 判断每个人是否满足
        for (OrgEmpTagInfoVo orgEmpTagInfoVo : userOrgEmpTagInfoVos) {
            if (!isMatch(orgEmpTagInfoVo, conditionGroup)) {
                return false;
            }
        }
        return true;
    }

    private boolean isMatch(OrgEmpTagInfoVo orgEmpTagInfoVo, Map<Integer, List<OrgEmpTagInfoVo>> conditionGroup) {
        for (Map.Entry<Integer, List<OrgEmpTagInfoVo>> entry : conditionGroup.entrySet()) {
            CustomFieldEnum customFieldEnum = CustomFieldEnum.getByNo(entry.getKey());
            switch (customFieldEnum) {
                case MEMBER:
                    if (new HashSet<>(entry.getValue()).containsAll(new HashSet<>(Collections.singletonList(orgEmpTagInfoVo)))) {
                        return true;
                    }
                    break;
                case ORG:
                    Set<String> conditionOrgInfo = entry.getValue().stream()
                            .map(i -> i.getEhrSource() + i.getId()).collect(Collectors.toSet());
                    if (syncEhrOrgInfoService.belongOrg(orgEmpTagInfoVo.getId(), conditionOrgInfo)) {
                        return true;
                    }
                    break;
	            case ORGANIZATION:
		            Set<String> ehrSourceOrgIdInfo = entry.getValue().stream().map(i -> i.getEhrSource() + i.getId()).collect(Collectors.toSet());
		            if (syncEhrOrgInfoService.belongParentOrg(orgEmpTagInfoVo.getId(), ehrSourceOrgIdInfo, Integer.valueOf(orgEmpTagInfoVo.getEhrSource()))) {
			            return true;
		            }
		            break;
                case TAG:
                    if (tUserRoleService.belongTag(orgEmpTagInfoVo.getId(),
                            entry.getValue().stream().map(OrgEmpTagInfoVo::getId).collect(Collectors.toList()))) {
                        return true;
                    }
                    break;
                default:
                    log.error("该controlDataType：{} 值不支持", entry.getKey());
            }
        }
        return false;
    }

    protected boolean strJudge(BpmnNodeConditionsConfBaseVo conditionsConf, BpmnStartConditionsVo bpmnStartConditionsVo, FourPredict<Object, Object, Object, Integer> predicate, int currentIndex) {
        Map<String, Object> lfConditionsFromDb = conditionsConf.getLfConditions();
        Map<String, Object> lfConditionsFromUser = bpmnStartConditionsVo.getLfConditions();
        if (CollectionUtils.isEmpty(lfConditionsFromDb)) {
            throw new JiMuBizException("the process has no no code conditions conf,please contact the administrator to add one");
        }
        if (ObjectUtils.isEmpty(lfConditionsFromUser)) {
            throw new JiMuBizException("the process has no no code form,please contact the administrator");
        }
        boolean isMatch = true;
        int iterIndex = 0;
        List<Integer> numberOperatorList = conditionsConf.getNumberOperatorList();
        //operator type
        for (Map.Entry<String, Object> stringObjectEntry : lfConditionsFromDb.entrySet()) {
            if (iterIndex != currentIndex) {
                iterIndex++;
                continue;
            }
            String key = stringObjectEntry.getKey();
            Integer numberOperator = numberOperatorList.get(iterIndex);
            Object valueFromUser = lfConditionsFromUser.get(key);
            if (valueFromUser == null) {
                throw new JiMuBizException(Strings.lenientFormat("condition field from user %s can not be null", key));
            }
            Object valueFromDb = stringObjectEntry.getValue();
            if (valueFromDb == null) {
                throw new JiMuBizException(Strings.lenientFormat("condition field from db %s can not be null", key));
            }

            String fieldTypeName = null;
            if (StringUtils.isNotEmpty(conditionsConf.getExtJson())) {
                JSONArray jsonArray = JSON.parseArray(conditionsConf.getExtJson());
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject jsonObject = jsonArray.getJSONObject(i);
                    if (jsonObject.getString("columnDbname").equals(key)) {
                        fieldTypeName = jsonObject.getString("fieldTypeName");
                        break;
                    }
                }
            }

            isMatch = predicate.test(fieldTypeName, valueFromDb, valueFromUser, numberOperator);
            iterIndex++;
            if (!isMatch) {
                return false;
            }
        }
        return isMatch;

    }
}
