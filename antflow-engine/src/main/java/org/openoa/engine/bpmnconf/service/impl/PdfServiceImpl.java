package org.openoa.engine.bpmnconf.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.aspose.words.SaveFormat;
import com.itextpdf.text.Document;
import com.itextpdf.text.pdf.PdfWriter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.openoa.base.constant.enums.EhrSourceEnum;
import org.openoa.base.constant.enums.NodeTypeEnum;
import org.openoa.base.constant.enums.ProcessStateEnum;
import org.openoa.base.dto.PdfPublicFields;
import org.openoa.base.entity.BpmBusinessProcess;
import org.openoa.base.exception.JiMuBizException;
import org.openoa.base.interf.BpmBusinessProcessService;
import org.openoa.base.interf.FormOperationAdaptor;
import org.openoa.base.util.PdfPrintReport;
import org.openoa.base.util.Watermark;
import org.openoa.base.vo.*;
import org.openoa.engine.bpmnconf.common.ProcessBusinessContans;
import org.openoa.engine.bpmnconf.confentity.BpmnNode;
import org.openoa.engine.bpmnconf.mapper.ProcessApprovalMapper;
import org.openoa.engine.bpmnconf.service.biz.BpmVerifyInfoBizServiceImpl;
import org.openoa.engine.factory.FormFactory;
import org.openoa.engine.lowflow.vo.UDLFApplyVo;
import org.openoa.engine.bpmnconf.service.PdfService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;


/**
* <AUTHOR>
* @description pdf信息组装
* @createDate 2025-04-27 14:44:57
*/
@Service
@SuppressWarnings("all")
@Slf4j
@RequiredArgsConstructor
public class PdfServiceImpl implements PdfService {

	private final BpmnConfServiceImpl bpmnConfService;
	private final BpmVerifyInfoBizServiceImpl bpmVerifyInfoBizService;
	private final ProcessApprovalMapper processApprovalMapper;
	private final FormFactory formFactory;
	private final BpmBusinessProcessService bpmBusinessProcessService;
	private final BpmnNodeServiceImpl bpmnNodeService;
	private final ProcessBusinessContans businessContans;


	/**
	 * <h3>审批流导出PDF数据组装</h3>
	 * @param document
	 * @param writer
	 * @param processNumber
	 */
	@Override
	public void export(Document document, PdfWriter writer, String values) {
		BusinessDataVo vo = JSON.parseObject(values, BusinessDataVo.class);
		String processNumber = vo.getProcessNumber();
		PdfPublicFields pdfPublicFields = new PdfPublicFields();
		boolean open = document.isOpen();
		if (!open) {
			document.open();
		}
		try { // 1-构建审批流的基础信息
			pdfPublicFields = getBaseInfo(processNumber, pdfPublicFields);
		} catch (Exception e) {
			log.error("构建审批流的基础信息出错={}, 流程编号={}", e.getMessage(), processNumber);
		}
		try { // 2-构建审批流的动态表单信息
			// pdfPublicFields = getDynamicFormInfo(vo, pdfPublicFields, values);
			pdfPublicFields = getDynamicFormInfoV2(vo, pdfPublicFields, values, false);
		} catch (Exception e) {
			log.error("构建审批流的动态表单信息出错={}, 流程编号={}", e.getMessage(), processNumber);
		}
		try { // 3-构建审批流的审批记录
			pdfPublicFields.setApprovalRecords(getApprovalRecord(processNumber));
		} catch (Exception e) {
			log.error("构建审批流的审批记录出错={}, 流程编号={}", e.getMessage(), processNumber);
		}
		try {
			// 创建并设置水印 - 必须在document.open()之前设置
			Watermark watermark = new Watermark(pdfPublicFields.getCompanyName());
			writer.setPageEvent(watermark);
			pdfPublicFields.setDocument(document);
			pdfPublicFields.setWriter(writer);
			document.open();
			new PdfPrintReport().generatePDF(pdfPublicFields);
		} catch (Exception ex) {
			log.error("PDF文件生成失败，请稍候再试，异常信息：{}", ex.getMessage());
		}

	}

	@Override
	public PdfPublicFields getFieldValues(String values) {
		BusinessDataVo vo = JSON.parseObject(values, BusinessDataVo.class);
		String processNumber = vo.getProcessNumber();
		PdfPublicFields pdfPublicFields = new PdfPublicFields();
		getBaseInfo(processNumber, pdfPublicFields);
		getDynamicFormInfoV2(vo, pdfPublicFields, values, true);
		return pdfPublicFields;
	}


	/**
	 * 获取PDF基本信息
	 * @param processNumber 流程编号
	 * @param pdfPublicFields PDF组装对象
	 * @return pdfPublicFields PDF组装对象
	 */
	public PdfPublicFields getBaseInfo(String processNumber, PdfPublicFields pdfPublicFields) {
		PdfBaseInfoVO baseInfo = processApprovalMapper.queryProcessBaseInfo(processNumber);
		pdfPublicFields.setEmpName(baseInfo.getEmpName());
		pdfPublicFields.setDeptName(baseInfo.getOrgName());
		pdfPublicFields.setDeptId(baseInfo.getOrgId());
		pdfPublicFields.setSubmitTime(baseInfo.getApplyTime());
		pdfPublicFields.setEndTime(baseInfo.getEndTime());
		pdfPublicFields.setApprovalStatusName(ProcessStateEnum.getDescByCode(baseInfo.getApprovalStatus()));
		pdfPublicFields.setCompanyName(EhrSourceEnum.getDescByCode(baseInfo.getEhrSource()));
		pdfPublicFields.setWaterMark(pdfPublicFields.getCompanyName());
		String empName = StringUtils.isBlank(baseInfo.getEmpName()) ? "" : baseInfo.getEmpName();
		String bpmnName = StringUtils.isBlank(baseInfo.getBpmnName()) ? "" : baseInfo.getBpmnName();
		pdfPublicFields.setPdfTitle(bpmnName);
		if (StringUtils.isAnyBlank(empName, bpmnName)) {
			log.error("PDF导出标题组装失败，获取员工名称或审批流模板名称失败：{}-{}-{}", empName, bpmnName, baseInfo.getBpmnConfId());
			pdfPublicFields.setPdfTitle("");
		}

		pdfPublicFields.setApprovalNumber(processNumber);
		pdfPublicFields.setBpmnConfId(baseInfo.getBpmnConfId());
		return pdfPublicFields;
	}

	/**
	 * 获取审批流程的表单信息，由于每个模板对应的表单信息都不一样，因此获取到的表单信息的字段名、字段值、排序都是不一样的
	 * <p>实现过程参考自 bpmnConf/process/viewBusinessProcess 这个接口</p>
	 * @param processNumber 当前审批编号
	 * @param pdfPublicFields PDF组装对象
	 * @return pdfPublicFields PDF组装对象
	 */
	public PdfPublicFields getDynamicFormInfo(BusinessDataVo businessDataVo, PdfPublicFields pdfPublicFields, String values) {
		BusinessDataVo vo = formFactory.dataFormConversion(values, businessDataVo.getFormCode());
		BpmBusinessProcess bpmBusinessProcess = bpmBusinessProcessService.getBpmBusinessProcess(vo.getProcessNumber());
		if(ObjectUtils.isEmpty(bpmBusinessProcess)){
			throw new JiMuBizException(String.format("动态获取流程的表单信息,processNumber%s,its data not in existence!",vo.getProcessNumber()));
		}
		vo.setBusinessId(bpmBusinessProcess.getBusinessId());
		FormOperationAdaptor formAdaptor = formFactory.getFormAdaptor(vo);
		businessDataVo = formAdaptor.queryData(vo);
		UDLFApplyVo data = (UDLFApplyVo) businessDataVo;
		if (ObjectUtils.isEmpty(data)) {
			log.warn("PDF导出表单信息中，没有获取到表单信息：{}， data:{}", values, data);
			return pdfPublicFields;
		}
		JSONArray widgetList = JSON.parseObject(data.getLfFormData()).getJSONArray("widgetList");
		Map<String, Object> lfFields = data.getLfFields();
		// 获取发起人的权限，有些表单控件可能对发起人不可见
		List<LFFieldControlVO> lfFieldControlVOs = new ArrayList<>();
		Map<String, String> fieldIdPermMap = new HashMap<>();
		ProcessRecordInfoVo processRecordInfo = businessContans.processInfo(bpmBusinessProcess);
		if (Objects.nonNull(processRecordInfo)) {
			lfFieldControlVOs = processRecordInfo.getLfFieldControlVOs();
		}
		if (!CollectionUtils.isEmpty(lfFieldControlVOs)) {
			Long startNodeId = Optional.ofNullable(bpmnNodeService.lambdaQuery()
				.select(BpmnNode::getId)
				.eq(BpmnNode::getConfId, pdfPublicFields.getBpmnConfId())
				.eq(BpmnNode::getNodeType, NodeTypeEnum.NODE_TYPE_START.getCode())
				.last("limit 1").one()).map(BpmnNode::getId).orElse(null);
			fieldIdPermMap = lfFieldControlVOs.stream().filter(x -> Objects.equals(startNodeId, x.getNodeId())).collect(Collectors.toMap(LFFieldControlVO::getFieldId, LFFieldControlVO::getPerm, (k, v) -> v));
		}
		if (ObjectUtils.isEmpty(lfFields)) {
			log.warn("PDF导出表单信息中，没有获取到表单信息lfFields：{}， data:{}", values, data);
			return pdfPublicFields;
		}
		List<PdfPublicFields.ApprovalContent> approvalContents = new ArrayList<>();
		try {
			Map<String, String> finalFieldIdPermMap = fieldIdPermMap;
			// widget组件关联控件(relatedControls)对关联组件的显示有影响，只展示关联上的组件，没有关联的组件要做隐藏处理hidden设置为true
			widgetList.forEach(widget -> {
				String type = JSON.parseObject(widget.toString()).getString("type");
				if (Arrays.asList("checkbox", "radio", "select").contains(type)) {
					JSONArray optionItems = JSON.parseObject(widget.toString()).getJSONObject("options").getJSONArray("optionItems");

					// 1. 收集所有关联控件ID
					Set<String> allRelatedControlIds = Optional.ofNullable(optionItems)
						.orElseGet(JSONArray::new)
						.stream()
						.map(obj -> (JSONObject) obj)
						.filter(option -> option.containsKey("relatedControls"))
						.flatMap(option -> option.getJSONArray("relatedControls").stream())
						.map(Object::toString)
						.collect(Collectors.toSet());

					// 2. 隐藏所有关联控件
					widgetList.stream().map(widgetObj -> (JSONObject) widgetObj)
						.filter(w -> allRelatedControlIds.contains(w.getString("id")))
						.forEach(w -> w.getJSONObject("options").put("hidden", true));

					// 3. 获取当前选中项的值 - 从lfFields中获取实际的表单数据
					String fieldId = JSON.parseObject(widget.toString()).getString("id");
					Object currentValue = lfFields.get(fieldId);

					// 4. 根据控件类型获取当前选中项的关联控件ID
					Set<String> currentRelatedControlIds = new HashSet<>();
					boolean isMultiple = "checkbox".equals(type) || ("select".equals(type) &&
						JSON.parseObject(widget.toString()).getJSONObject("options").getBooleanValue("multiple"));

					if (isMultiple) {
						// 多选情况：处理数组形式的值
						if (currentValue != null) {
							JSONArray selectedValues = null;

							// 处理不同格式的多选值
							if (currentValue instanceof JSONArray) {
								selectedValues = (JSONArray) currentValue;
							} else if (currentValue instanceof String) {
								String valueStr = currentValue.toString();
								// 处理 [1,2] 格式
								if (valueStr.startsWith("[") && valueStr.endsWith("]")) {
									selectedValues = JSON.parseArray(valueStr);
								}
								// 处理 "1,2" 格式
								else if (valueStr.contains(",")) {
									selectedValues = new JSONArray();
									for (String val : valueStr.split(",")) {
										selectedValues.add(val.trim());
									}
								} else {
									// 单个值也当作数组处理
									selectedValues = new JSONArray();
									selectedValues.add(valueStr);
								}
							}

							if (selectedValues != null) {
								for (int i = 0; i < selectedValues.size(); i++) {
									String value = selectedValues.getString(i);
									// 查找匹配的选项并收集关联控件ID
									for (int j = 0; j < optionItems.size(); j++) {
										JSONObject option = optionItems.getJSONObject(j);
										if (option.getString("value").equals(value)) {
											if (option.containsKey("relatedControls")) {
												JSONArray relatedControls = option.getJSONArray("relatedControls");
												for (int k = 0; k < relatedControls.size(); k++) {
													currentRelatedControlIds.add(relatedControls.getString(k));
												}
											}
											break; // 找到匹配项后跳出内层循环
										}
									}
								}
							}
						}
					} else { // 单选情况：currentValue 是字符串或数字
						String selectedValue = currentValue != null ? currentValue.toString() : null;
						if (selectedValue != null) {
							for (int i = 0; i < optionItems.size(); i++) {
								JSONObject option = optionItems.getJSONObject(i);
								if (option.getString("value").equals(selectedValue)) {
									if (option.containsKey("relatedControls")) {
										JSONArray relatedControls = option.getJSONArray("relatedControls");
										for (int j = 0; j < relatedControls.size(); j++) {
											currentRelatedControlIds.add(relatedControls.getString(j));
										}
									}
									break; // 单选找到后即可退出
								}
							}
						}
					}

					// 5. 显示当前选中项关联的控件
					for (Object widgetObj : widgetList) {
						JSONObject w = (JSONObject) widgetObj;
						String id = w.getString("id");
						if (currentRelatedControlIds.contains(id)) {
							w.getJSONObject("options").put("hidden", false);
						}
					}
				}
			});
			widgetList.forEach(widget -> { // 多种类型的wight组件适配
				String type = JSON.parseObject(widget.toString()).getString("type");
				boolean isHidden = JSON.parseObject(widget.toString()).getJSONObject("options").getBoolean("hidden");
				String perm = finalFieldIdPermMap.get(JSON.parseObject(widget.toString()).getString("id"));
				boolean display = !isHidden;
				if (Objects.equals("H", perm)) {
					display = false;
				}
				if (display) {
					if (Arrays.asList("checkbox", "radio", "select").contains(type)) { // 多选框、单选框、下拉框适配
						String label = JSON.parseObject(widget.toString()).getJSONObject("options").getString("label");
						String valLf = lfFields.getOrDefault(JSON.parseObject(widget.toString()).getString("id"), "").toString();
						// 如果valLf是[1,2]或者1,2 这种数组类型，则需要转换为多个val，中间用顿号、分割成vals的List<String>
						StringBuilder val = new StringBuilder("");
						if (valLf.startsWith("[") && valLf.endsWith("]")) {
							List<String> vals = JSON.parseArray(valLf, String.class);
							for (String s : vals) {
								val.append(JSON.parseObject(widget.toString()).getJSONObject("options").getJSONArray("optionItems").stream()
									.filter(item -> JSON.parseObject(item.toString()).getString("value").equals(s))
									.map(item -> JSON.parseObject(item.toString()).getString("label"))
									.findFirst().orElse(""));
								if (!s.equals(vals.get(vals.size() - 1))) {
									val.append("、");
								}
							}
						}  else if (valLf.contains(",")) {
							List<String> vals = Arrays.asList(valLf.split(","));
							for (String s : vals) {
								val.append(JSON.parseObject(widget.toString()).getJSONObject("options").getJSONArray("optionItems").stream()
									.filter(item -> JSON.parseObject(item.toString()).getString("value").equals(valLf))
									.map(item -> JSON.parseObject(item.toString()).getString("label"))
									.findFirst().orElse(""));
								if (!s.equals(vals.get(vals.size() - 1))) {
									val.append("、");
								}
							}
						} else {
							// 获取optionItems里value等于valField的label值
							val.append(JSON.parseObject(widget.toString()).getJSONObject("options").getJSONArray("optionItems").stream()
								.filter(item -> JSON.parseObject(item.toString()).getString("value").equals(valLf))
								.map(item -> JSON.parseObject(item.toString()).getString("label"))
								.findFirst().orElse(""));
						}
						String v = val.toString();
						log.info("---> checkbox、radio、select：{} -> {}", label, v);
						PdfPublicFields.ApprovalContent approvalContent = new PdfPublicFields.ApprovalContent();
						approvalContent.setFieldName(label);
						approvalContent.setFieldValue(v);
						approvalContent.setType(type);
						approvalContents.add(approvalContent);
					} else if (Arrays.asList("cascader").contains(type)) {
						String label = JSON.parseObject(widget.toString()).getJSONObject("options").getString("label");
						String valField = lfFields.getOrDefault(JSON.parseObject(widget.toString()).getJSONObject("options").getString("name"), "").toString();
						JSONArray optItems = JSON.parseObject(widget.toString()).getJSONObject("options").getJSONArray("optionItems");
						String val = cascaderInfoAdaptation(valField, optItems);
						PdfPublicFields.ApprovalContent approvalContent = new PdfPublicFields.ApprovalContent();
						approvalContent.setType(type);
						approvalContent.setFieldName(label);
						approvalContent.setFieldValue(val);
						approvalContents.add(approvalContent);
						log.info("---> cascader：{} -> {}", label, val);
					} else if (Arrays.asList("time-range", "date-range").contains(type)) { // 日期或时间的范围适配
						String label = JSON.parseObject(widget.toString()).getJSONObject("options").getString("label");
						String valField = JSON.parseObject(widget.toString()).getJSONObject("options").getString("name");
						JSONArray dateTimeRange = JSON.parseArray(lfFields.getOrDefault(valField, "").toString());
						// 取出dateTimeRange的所有元素，中间用" 至 "连接
						String val = dateTimeRange.stream().map(Object::toString).collect(Collectors.joining(" 至 "));
						log.info("---> time-range、date-range：{} -> {}", label, val);
						PdfPublicFields.ApprovalContent approvalContent = new PdfPublicFields.ApprovalContent();
						approvalContent.setType(type);
						approvalContent.setFieldName(label);
						approvalContent.setFieldValue(val);
						approvalContents.add(approvalContent);
					} else if (Arrays.asList("rich-editor").contains(type)) { // 富文本适配
						String label = JSON.parseObject(widget.toString()).getJSONObject("options").getString("label");
						String valField = JSON.parseObject(widget.toString()).getJSONObject("options").getString("name");
						log.info("---> rich-editor：{} -> {}", label, lfFields.getOrDefault(valField, "").toString());
						PdfPublicFields.ApprovalContent approvalContent = new PdfPublicFields.ApprovalContent();
						approvalContent.setType(type);
						approvalContent.setFieldName(label);
						approvalContent.setFieldValue(lfFields.getOrDefault(valField, "").toString());
						approvalContent.setType("rich-editor"); // 必须加，否则不会渲染富文本
						approvalContents.add(approvalContent);
					} else if (Arrays.asList("member", "organization", "file-upload").contains(type)) { // 成员、组织、文件上传适配
						String label = JSON.parseObject(widget.toString()).getJSONObject("options").getString("label");
						String valField = JSON.parseObject(widget.toString()).getJSONObject("options").getString("name");
						JSONArray valArr = JSON.parseArray(lfFields.getOrDefault(valField, "").toString());
						if (valArr != null) {
							String val = valArr.stream().map(item -> JSON.parseObject(item.toString()).getString("name")).collect(Collectors.joining("\n"));
							log.info("---> member、organization、file-upload：{} -> {}", label, val);
							PdfPublicFields.ApprovalContent approvalContent = new PdfPublicFields.ApprovalContent();
							approvalContent.setType(type);
							approvalContent.setFieldName(label);
							approvalContent.setFieldValue(val);
							approvalContents.add(approvalContent);
						} else {
							log.warn("PDF导出-获取表单信息时部分组件获取失败，请检查数据是否保存完整 type={}, label={}, valField={}", type, label, valField);
						}

					} else if (Arrays.asList("picture-upload").contains(type)) { // 图片上传适配
						String label = JSON.parseObject(widget.toString()).getJSONObject("options").getString("label");
						String valField = JSON.parseObject(widget.toString()).getJSONObject("options").getString("name");
						JSONArray valArr = JSON.parseArray(lfFields.getOrDefault(valField, "").toString());
						String val = valArr.stream().map(item -> JSON.parseObject(item.toString()).getString("url")).collect(Collectors.joining("\n"));
						log.info("---> picture-upload：{} -> {}", label, val);
						PdfPublicFields.ApprovalContent approvalContent = new PdfPublicFields.ApprovalContent();
						approvalContent.setType(type);
						approvalContent.setFieldName(label);
						approvalContent.setFieldValue(val);
						approvalContent.setType("picture-upload"); // 必须加，否则不会渲染图片
						approvalContents.add(approvalContent);
					} else if (Arrays.asList("alert").contains(type)) { // 说明alert
						String title = JSON.parseObject(widget.toString()).getJSONObject("options").getString("title");
						String description = JSON.parseObject(widget.toString()).getJSONObject("options").getString("description");
						PdfPublicFields.ApprovalContent approvalContent = new PdfPublicFields.ApprovalContent();
						approvalContent.setFieldName(title);
						approvalContent.setFieldValue(description);
						approvalContent.setType(type);
						approvalContents.add(approvalContent);
					} else { // 不需要单独适配
						String label = JSON.parseObject(widget.toString()).getJSONObject("options").getString("label");
						String valField = JSON.parseObject(widget.toString()).getJSONObject("options").getString("name");
						PdfPublicFields.ApprovalContent approvalContent = new PdfPublicFields.ApprovalContent();
						approvalContent.setType(type);
						approvalContent.setFieldName(label);
						approvalContent.setFieldValue(lfFields.getOrDefault(valField, "").toString());
						approvalContents.add(approvalContent);
					}
				}
			});
		} catch (Exception e) {
			log.warn("error on forEach", e);
			throw e;
		}

		log.info("PDF导出表单信息中，获取到的表单信息：{}", JSON.toJSONString(approvalContents));
		pdfPublicFields.setApprovalContents(approvalContents);
		return pdfPublicFields;
	}


	/**
	 * 级联选择器适配
	 * @param valField valField是一个二维数组，值为[[1,11],[2,3]] 因为里面是两个一维数组，意思是选中了级联选择器中的两个选项，分别是1里面的children=11和2里面的children=3。现在我想获取最里层children里label的值
	 * @param optItems label和value的组成的Json数组
	 */
	private String cascaderInfoAdaptation(String valField, JSONArray optItems) {
		List<String> results = new ArrayList<>();
		try {
			// 尝试将 valField 解析为 JSONArray
			JSONArray valArray = JSON.parseArray(valField);
			List<JSONArray> paths = new ArrayList<>();
			// 判断 valArray 是否是二维数组
			if (!valArray.isEmpty() && valArray.get(0) instanceof JSONArray) {
				// 二维数组，每个元素是一个路径
				for (Object obj : valArray) {
					if (obj instanceof JSONArray) {
						paths.add((JSONArray) obj);
					}
				}
			} else {
				// 一维数组或非数组，整体视为一条路径
				paths.add(valArray);
			}
			// 遍历每条路径
			for (JSONArray pathArray : paths) {
				List<String> labels = new ArrayList<>();
				JSONArray currentLevel = optItems;
				boolean foundAll = true;
				for (int i = 0; i < pathArray.size(); i++) {
					Object currentVal = pathArray.get(i); // 当前层级的值
					JSONObject matchedNode = null;
					// 遍历当前层级的选项，查找匹配的 value
					for (Object nodeObj : currentLevel) {
						JSONObject node = (JSONObject) nodeObj;
						Object nodeValue = node.get("value");
						// 比较值，兼容 Integer 和 String 类型
						if (currentVal instanceof Number && nodeValue instanceof Number) {
							if (((Number) currentVal).intValue() == ((Number) nodeValue).intValue()) {
								matchedNode = node;
								break;
							}
						} else if (currentVal.equals(nodeValue)) {
							matchedNode = node;
							break;
						}
					}
					if (matchedNode == null) {
						foundAll = false;
						break;
					}
					labels.add(matchedNode.getString("label"));

					// 如果不是最后一个元素，进入下一层级
					if (i < pathArray.size() - 1) {
						JSONArray children = matchedNode.getJSONArray("children");
						if (children == null || children.isEmpty()) {
							foundAll = false;
							break;
						}
						currentLevel = children;
					}
				}
				// 如果路径全部匹配成功，加入结果
				if (foundAll && !labels.isEmpty()) {
					results.add(String.join(" / ", labels));
				}
			}
		} catch (Exception e) {
			log.warn("解析级联选择器数据时发生错误：{}", valField, e);
		}
		// 将所有路径结果拼接为多行字符串
		String val = String.join("\n", results);
		return val;
	}


	/**
	 * 获取审批记录
	 * @param processNumber 审批编号
	 * @return 审批记录列表
	 */
	public List<PdfPublicFields.ApprovalRecord> getApprovalRecord(String processNumber) {
		if (StringUtils.isBlank(processNumber)) {
			return Collections.emptyList();
		}
		List<BpmVerifyInfoVo> bpmVerifyInfoVos = bpmVerifyInfoBizService.getBpmVerifyInfoVos(processNumber, false);
		if (CollectionUtils.isEmpty(bpmVerifyInfoVos)) {
			return Collections.emptyList();
		}
		List<PdfPublicFields.ApprovalRecord> rs = new ArrayList<>();
		for (BpmVerifyInfoVo bpmVerify : bpmVerifyInfoVos) {
			rs.add(
				PdfPublicFields.ApprovalRecord.builder()
					.taskName(bpmVerify.getTaskName())
					.verifyUserName(bpmVerify.getVerifyUserName())
					.verifyStatusName(bpmVerify.getVerifyStatusName())
					.verifyDesc(bpmVerify.getVerifyDesc())
					.verifyDate(bpmVerify.getVerifyDate())
					.build()
			);
		}
		return rs;
	}

	/**
	 * 获取审批流程的表单信息v2版本
	 * <p>
	 *     与👆🏻getDynamicFormInfo()方法不同的是：getDynamicFormInfoV2()会返回所有可见的表单信息,去掉了关联控件的隐藏关系和针对发起人的表单权限控制
	 * </p>
	 * @param processNumber 当前审批编号
	 * @param pdfPublicFields PDF组装对象
	 * @return pdfPublicFields PDF组装对象
	 */
	public PdfPublicFields getDynamicFormInfoV2(BusinessDataVo businessDataVo, PdfPublicFields pdfPublicFields, String values
			, boolean isInludeHiddenField) {
		BusinessDataVo vo = formFactory.dataFormConversion(values, businessDataVo.getFormCode());
		BpmBusinessProcess bpmBusinessProcess = bpmBusinessProcessService.getBpmBusinessProcess(vo.getProcessNumber());
		if(ObjectUtils.isEmpty(bpmBusinessProcess)){
			throw new JiMuBizException(String.format("动态获取流程的表单信息,processNumber%s,its data not in existence!",vo.getProcessNumber()));
		}
		vo.setBusinessId(bpmBusinessProcess.getBusinessId());
		FormOperationAdaptor formAdaptor = formFactory.getFormAdaptor(vo);
		businessDataVo = formAdaptor.queryData(vo);
		UDLFApplyVo data = (UDLFApplyVo) businessDataVo;
		if (ObjectUtils.isEmpty(data)) {
			log.warn("PDF导出表单信息中，没有获取到表单信息：{}， data:{}", values, data);
			return pdfPublicFields;
		}
		JSONArray widgetList = JSON.parseObject(data.getLfFormData()).getJSONArray("widgetList");
		Map<String, Object> lfFields = data.getLfFields();

		if (ObjectUtils.isEmpty(lfFields)) {
			log.warn("PDF导出表单信息中，没有获取到表单信息lfFields：{}， data:{}", values, data);
			return pdfPublicFields;
		}
		List<PdfPublicFields.ApprovalContent> approvalContents = new ArrayList<>();
		try {
			widgetList.forEach(widget -> { // 多种类型的wight组件适配
				String type = JSON.parseObject(widget.toString()).getString("type");
				boolean isHidden = JSON.parseObject(widget.toString()).getJSONObject("options").getBoolean("hidden");
				if (isInludeHiddenField || !isHidden) {
					if (Arrays.asList("checkbox", "radio", "select").contains(type)) { // 多选框、单选框、下拉框适配
						String label = JSON.parseObject(widget.toString()).getJSONObject("options").getString("label");
						String fieldId = JSON.parseObject(widget.toString()).getString("id");
						String valLf = lfFields.getOrDefault(fieldId, "").toString();
						// 如果valLf是[1,2]或者1,2 这种数组类型，则需要转换为多个val，中间用顿号、分割成vals的List<String>
						StringBuilder val = new StringBuilder("");
						if (valLf.startsWith("[") && valLf.endsWith("]")) {
							List<String> vals = JSON.parseArray(valLf, String.class);
							for (String s : vals) {
								val.append(JSON.parseObject(widget.toString()).getJSONObject("options").getJSONArray("optionItems").stream()
									.filter(item -> JSON.parseObject(item.toString()).getString("value").equals(s))
									.map(item -> JSON.parseObject(item.toString()).getString("label"))
									.findFirst().orElse(""));
								if (!s.equals(vals.get(vals.size() - 1))) {
									val.append("、");
								}
							}
						}  else if (valLf.contains(",")) {
							List<String> vals = Arrays.asList(valLf.split(","));
							for (String s : vals) {
								val.append(JSON.parseObject(widget.toString()).getJSONObject("options").getJSONArray("optionItems").stream()
									.filter(item -> JSON.parseObject(item.toString()).getString("value").equals(valLf))
									.map(item -> JSON.parseObject(item.toString()).getString("label"))
									.findFirst().orElse(""));
								if (!s.equals(vals.get(vals.size() - 1))) {
									val.append("、");
								}
							}
						} else {
							// 获取optionItems里value等于valField的label值
							val.append(JSON.parseObject(widget.toString()).getJSONObject("options").getJSONArray("optionItems").stream()
								.filter(item -> JSON.parseObject(item.toString()).getString("value").equals(valLf))
								.map(item -> JSON.parseObject(item.toString()).getString("label"))
								.findFirst().orElse(""));
						}
						String v = val.toString();
						log.info("---> checkbox、radio、select：{} -> {}", label, v);
						PdfPublicFields.ApprovalContent approvalContent = new PdfPublicFields.ApprovalContent();
						approvalContent.setFieldName(label);
						approvalContent.setFieldValue(v);
						approvalContent.setType(type);
						approvalContent.setFieldId(fieldId);
						approvalContents.add(approvalContent);
					} else if (Arrays.asList("cascader").contains(type)) {
						String label = JSON.parseObject(widget.toString()).getJSONObject("options").getString("label");
						String fieldId = JSON.parseObject(widget.toString()).getJSONObject("options").getString("name");
						String valField = lfFields.getOrDefault(fieldId, "").toString();
						JSONArray optItems = JSON.parseObject(widget.toString()).getJSONObject("options").getJSONArray("optionItems");
						String val = cascaderInfoAdaptation(valField, optItems);
						PdfPublicFields.ApprovalContent approvalContent = new PdfPublicFields.ApprovalContent();
						approvalContent.setType(type);
						approvalContent.setFieldName(label);
						approvalContent.setFieldValue(val);
						approvalContent.setFieldId(fieldId);
						approvalContents.add(approvalContent);
						log.info("---> cascader：{} -> {}", label, val);
					} else if (Arrays.asList("time-range", "date-range").contains(type)) { // 日期或时间的范围适配
						String label = JSON.parseObject(widget.toString()).getJSONObject("options").getString("label");
						String fieldId = JSON.parseObject(widget.toString()).getJSONObject("options").getString("name");
						JSONArray dateTimeRange = JSON.parseArray(lfFields.getOrDefault(fieldId, "").toString());
						if (dateTimeRange != null) {
							// 取出dateTimeRange的所有元素，中间用" 至 "连接
							String val = dateTimeRange.stream().map(Object::toString)
								.collect(Collectors.joining(" 至 "));
							log.info("---> time-range、date-range：{} -> {}", label, val);
							PdfPublicFields.ApprovalContent approvalContent = new PdfPublicFields.ApprovalContent();
							approvalContent.setType(type);
							approvalContent.setFieldName(label);
							approvalContent.setFieldValue(val);
							approvalContent.setFieldId(fieldId);
							approvalContents.add(approvalContent);
						}
					} else if (Arrays.asList("rich-editor").contains(type)) { // 富文本适配
						String label = JSON.parseObject(widget.toString()).getJSONObject("options").getString("label");
						String fieldId = JSON.parseObject(widget.toString()).getJSONObject("options").getString("name");
						log.info("---> rich-editor：{} -> {}", label, lfFields.getOrDefault(fieldId, "").toString());
						PdfPublicFields.ApprovalContent approvalContent = new PdfPublicFields.ApprovalContent();
						approvalContent.setType(type);
						approvalContent.setFieldName(label);
						approvalContent.setFieldId(fieldId);
						approvalContent.setFieldValue(lfFields.getOrDefault(fieldId, "").toString());
						approvalContent.setType("rich-editor"); // 必须加，否则不会渲染富文本
						approvalContents.add(approvalContent);
					} else if (Arrays.asList("member", "organization", "file-upload").contains(type)) { // 成员、组织、文件上传适配
						String label = JSON.parseObject(widget.toString()).getJSONObject("options").getString("label");
						String fieldId = JSON.parseObject(widget.toString()).getJSONObject("options").getString("name");
						JSONArray valArr = JSON.parseArray(lfFields.getOrDefault(fieldId, "").toString());
						if (valArr != null) {
							String val = valArr.stream().map(item -> JSON.parseObject(item.toString()).getString("name")).collect(Collectors.joining("\n"));
							log.info("---> member、organization、file-upload：{} -> {}", label, val);
							PdfPublicFields.ApprovalContent approvalContent = new PdfPublicFields.ApprovalContent();
							approvalContent.setType(type);
							approvalContent.setFieldName(label);
							approvalContent.setFieldValue(val);
							approvalContent.setFieldId(fieldId);
							approvalContents.add(approvalContent);
						} else {
							log.warn("PDF导出-获取表单信息时部分组件获取失败，请检查数据是否保存完整 type={}, label={}, valField={}", type, label, fieldId);
						}

					} else if (Arrays.asList("picture-upload").contains(type)) { // 图片上传适配
						String label = JSON.parseObject(widget.toString()).getJSONObject("options").getString("label");
						String fieldId = JSON.parseObject(widget.toString()).getJSONObject("options").getString("name");
						JSONArray valArr = JSON.parseArray(lfFields.getOrDefault(fieldId, "").toString());
						String val = valArr.stream().map(item -> JSON.parseObject(item.toString()).getString("url")).collect(Collectors.joining("\n"));
						log.info("---> picture-upload：{} -> {}", label, val);
						PdfPublicFields.ApprovalContent approvalContent = new PdfPublicFields.ApprovalContent();
						approvalContent.setType(type);
						approvalContent.setFieldName(label);
						approvalContent.setFieldValue(val);
						approvalContent.setFieldId(fieldId);
						approvalContent.setType("picture-upload"); // 必须加，否则不会渲染图片
						approvalContents.add(approvalContent);
					} else if (Arrays.asList("alert").contains(type)) { // 说明alert
						String title = JSON.parseObject(widget.toString()).getJSONObject("options").getString("title");
						String description = JSON.parseObject(widget.toString()).getJSONObject("options").getString("description");
						String fieldId = JSON.parseObject(widget.toString()).getJSONObject("options").getString("name");
						PdfPublicFields.ApprovalContent approvalContent = new PdfPublicFields.ApprovalContent();
						approvalContent.setFieldName(title);
						approvalContent.setFieldValue(description);
						approvalContent.setType(type);
						approvalContent.setFieldId(fieldId);
						approvalContents.add(approvalContent);
					} else { // 不需要单独适配
						String label = JSON.parseObject(widget.toString()).getJSONObject("options").getString("label");
						String fieldId = JSON.parseObject(widget.toString()).getJSONObject("options").getString("name");
						PdfPublicFields.ApprovalContent approvalContent = new PdfPublicFields.ApprovalContent();
						approvalContent.setType(type);
						approvalContent.setFieldName(label);
						approvalContent.setFieldId(fieldId);
						approvalContent.setFieldValue(lfFields.getOrDefault(fieldId, "").toString());
						approvalContents.add(approvalContent);
					}
				}
			});
		} catch (Exception e) {
			log.warn("error on forEach", e);
			throw e;
		}

		log.info("PDF导出表单信息中，获取到的表单信息：{}", JSON.toJSONString(approvalContents));
		pdfPublicFields.setApprovalContents(approvalContents);
		return pdfPublicFields;
	}

}




