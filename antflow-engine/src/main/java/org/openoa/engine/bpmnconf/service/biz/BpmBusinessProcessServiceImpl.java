package org.openoa.engine.bpmnconf.service.biz;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.util.DateUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.HistoryService;
import org.activiti.engine.history.HistoricProcessInstance;
import org.activiti.engine.history.HistoricTaskInstance;
import org.activiti.engine.impl.pvm.PvmActivity;
import org.activiti.engine.impl.pvm.process.ActivityImpl;
import org.apache.commons.lang3.StringUtils;
import org.openoa.base.constant.enums.ProcessEnum;
import org.openoa.base.constant.enums.ProcessStateEnum;
import org.openoa.base.dto.PdfPublicFields;
import org.openoa.base.dto.wecom.NodeHeadActionDto;
import org.openoa.base.dto.wecom.NodeHeadActionRunTaskDto;
import org.openoa.base.entity.BpmBusinessProcess;
import org.openoa.base.entity.SyncEhrOrgInfo;
import org.openoa.base.entity.User;
import org.openoa.base.exception.JiMuBizException;
import org.openoa.base.interf.BpmBusinessProcessService;
import org.openoa.base.mapper.UserMapper;
import org.openoa.base.service.SyncEhrOrgInfoService;
import org.openoa.base.service.UserServiceImpl;
import org.openoa.base.service.wecom.NoticeMsgEnum;
import org.openoa.base.util.SecurityUtils;
import org.openoa.base.vo.BaseIdTranStruVo;
import org.openoa.base.vo.BpmBusinessProcessExportVo;
import org.openoa.base.vo.BpmVerifyInfoVo;
import org.openoa.base.vo.BpmnNodeVo;
import org.openoa.base.vo.MakeProcessEndReqVo;
import org.openoa.engine.bpmnconf.common.ActivitiAdditionalInfoServiceImpl;
import org.openoa.engine.bpmnconf.confentity.BpmVariable;
import org.openoa.engine.bpmnconf.confentity.BpmnConf;
import org.openoa.engine.bpmnconf.mapper.BpmBusinessProcessMapper;
import org.openoa.engine.bpmnconf.mapper.BpmnConfMapper;
import org.openoa.engine.bpmnconf.service.PdfService;
import org.openoa.engine.bpmnconf.mapper.BpmVerifyInfoMapper;
import org.openoa.engine.bpmnconf.service.impl.BpmVariableServiceImpl;
import org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * this is the core service for connecting bpmn and business
 */
@Slf4j
@Service
public class BpmBusinessProcessServiceImpl extends ServiceImpl<BpmBusinessProcessMapper, BpmBusinessProcess> implements BpmBusinessProcessService {


    @Autowired
    private BpmBusinessProcessMapper mapper;
	@Autowired
	private BpmVerifyInfoBizServiceImpl bpmVerifyInfoBizService;
	@Autowired
	private BpmVariableServiceImpl bpmVariableService;
	@Autowired
	private HistoryService historyService;
	@Autowired
	private ActivitiAdditionalInfoServiceImpl activitiAdditionalInfoService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;
	@Resource
	private UserServiceImpl userService;
	@Resource
	private UserMapper userMapper;
	@Resource
	private BpmnConfMapper bpmnConfMapper;
	@Resource
	private PdfService pdfService;
	@Resource
	private SyncEhrOrgInfoService syncEhrOrgInfoService;
	private static final String FIELD_NAME_ID_SEPARATOR = "#";
	@Resource
	private NotifyServiceImpl notifyService;
	@Resource
	private BpmnConfServiceImpl bpmnConfService;
	@Resource
	private BpmVerifyInfoMapper bpmVerifyInfoMapper;


    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void saveProcessNodeIdList(List<BpmnNodeVo> nodes, String processNum){
        // 找到首节点
        List<BpmnNodeVo> startNodeList = nodes.stream().filter(n -> "Gb2".equals(n.getNodeId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(startNodeList)){
            log.error("获取首节点异常！获取的首节点列表={}", startNodeList);
            return;
        }
        BpmnNodeVo startNode = startNodeList.get(0);

        // 将所有的节点转为map
        Map<String, BpmnNodeVo> nodeMap = nodes.stream().collect(Collectors.toMap(BpmnNodeVo::getNodeId, i -> i));

        StringBuffer nodeIds = new StringBuffer();
        nodeIds.append(startNode.getId());
        appendNodeIds(startNode.getNodeId(), nodeIds, nodeMap);

//        update(new LambdaUpdateWrapper<BpmBusinessProcess>()
//                .eq(BpmBusinessProcess::getBusinessNumber, processNum)
//                .set(BpmBusinessProcess::getNodeIdList, nodeIds.toString()));

        stringRedisTemplate.opsForValue().set(processNum, nodeIds.toString());
    }

    /**
     * 使用深度优先遍历递归，将所有节点的id添加到nodeIds中
     * @param currentNodeId
     * @param nodeIds
     * @param nodeMap
     */
    private void appendNodeIds(String currentNodeId, StringBuffer nodeIds, Map<String, BpmnNodeVo> nodeMap) {
        BpmnNodeVo curNodeVo = nodeMap.get(currentNodeId);
        List<String> tos = curNodeVo.getNodeTo();
        if (CollectionUtils.isEmpty(tos)) {
            return;
        }
        for (String to : tos) {
            BpmnNodeVo toNode = nodeMap.get(to);
            if (toNode == null) {
                continue;
            }
            nodeIds.append(",").append(toNode.getId());
            appendNodeIds(to, nodeIds, nodeMap);
        }
    }


    /**
     * find bpmBusinessProcess by business id and business number
     *
     * @param
     * @param businessId business id
     * @return
     */
    public BpmBusinessProcess findBpmBusinessProcess(String businessId, String businessNumber) {
        return mapper.findBpmBusinessProcess(BpmBusinessProcess.builder().businessNumber(businessNumber).businessId(businessId).build());
    }

    /**
     * save business and process relation data
     *
     * @param businessProcess
     * @return
     * @throws Exception
     */
    @Transactional(propagation = Propagation.REQUIRED)
    public void addBusinessProcess(BpmBusinessProcess businessProcess) {
        mapper.insert(businessProcess);
    }


    public void addBusinessProcess(String businessId, String key, String entryId, String processNum,String bpmnCode, String description) {
        BpmBusinessProcess bpmBusinessProcess = new BpmBusinessProcess();

        bpmBusinessProcess.setVersion(bpmnCode);
        Date nowDate = new Date();
        bpmBusinessProcess.setCreateTime(nowDate);
        bpmBusinessProcess.setUpdateTime(nowDate);
        bpmBusinessProcess.setBusinessId(businessId);
        bpmBusinessProcess.setProcessinessKey(key);
        bpmBusinessProcess.setEntryId(entryId);
        bpmBusinessProcess.setDescription(description);
        bpmBusinessProcess.setBusinessNumber(processNum);
        bpmBusinessProcess.setProcessState(ProcessEnum.COMLETE_STATE.getCode());
        mapper.insert(bpmBusinessProcess);
    }

    /**
     * update business process state
     *
     * @param
     * @return
     */
    public BpmBusinessProcess updateBusinessProcess(BpmBusinessProcess bpmBusinessProcess) {
        QueryWrapper<BpmBusinessProcess> wrapper = new QueryWrapper<>();

        wrapper.eq("BUSINESS_NUMBER", bpmBusinessProcess.getBusinessNumber());
        List<BpmBusinessProcess> bpmBusinessProcesses = mapper.selectList(wrapper);
        bpmBusinessProcesses.forEach(o -> {
            o.setProcessState(bpmBusinessProcess.getProcessState());
            if (!ObjectUtils.isEmpty(bpmBusinessProcess)) {
                o.setDescription(bpmBusinessProcess.getDescription());
            }
            mapper.updateById(o);
        });
        return bpmBusinessProcesses.get(0);
    }

    /**
     * query bpmBusinessProcess by process number
     */
    public BpmBusinessProcess getBpmBusinessProcess(String processCode) {
        QueryWrapper<BpmBusinessProcess> wrapper = new QueryWrapper<>();
        wrapper.eq("BUSINESS_NUMBER", processCode);
        BpmBusinessProcess bpmBusinessProcess = this.getOne(wrapper);

        return bpmBusinessProcess;
    }


    /**
     * get bpmBusinessProcess by bpmBusinessProcess Entity
     */
    public BpmBusinessProcess getBpmBusinessProcess(BpmBusinessProcess bpmBusinessProcess) {
        return mapper.findBpmBusinessProcess(bpmBusinessProcess);
    }


    /**
     * Existence or not
     */
    public Boolean isExist(BpmBusinessProcess bpmBusinessProcess) {
        if (null == bpmBusinessProcess) {
            return false;
        }
        Integer count = mapper.isExist(bpmBusinessProcess);
        return null != count && count > 0;
    }


    /**
     * update bpmBusinessProcess by entry id
     *
     * @param entryId
     * @return
     */
    public boolean updateBpmBusinessProcess(String entryId) {
        QueryWrapper<BpmBusinessProcess> wrapper = new QueryWrapper<>();
        wrapper.eq("ENTRY_ID", entryId);
        mapper.selectList(wrapper).stream().forEach(o -> {
            o.setProcessState(ProcessStateEnum.END_STATE.getCode());
            mapper.updateById(o);
        });
        return true;
    }

    /**
     * get process tittles by process numbers
     * @param processNumbers
     * @return
     */
    public List<BpmBusinessProcess> listBpmBusinessProcess(List<String> processNumbers) {
        List<BpmBusinessProcess> result = new ArrayList<>();
        if (ObjectUtils.isEmpty(processNumbers)) {
            return result;
        }
        QueryWrapper<BpmBusinessProcess> wrapper = new QueryWrapper<>();

        wrapper.select("BUSINESS_NUMBER", "description");
        wrapper.in("BUSINESS_NUMBER", processNumbers);
        result = this.mapper.selectList(wrapper);
        return result;
    }

    /**
     * check whether there is duplicate data
     */
    public boolean checkData(String processNumber) {
        long number = this.mapper.selectCount(new QueryWrapper<BpmBusinessProcess>().eq("BUSINESS_NUMBER", processNumber));
        return number <= 0;
    }

    /**
     * check whether there is duplicate data by entry id
     */
    public boolean checkProcessData(String entryId) {
        long number = this.mapper.selectCount(new QueryWrapper<BpmBusinessProcess>().eq("ENTRY_ID", entryId));
        return number <= 0;
    }

    /**
     * update process's isDel field
     */
    public void updateProcessIsDel(String processNumber) {
        BpmBusinessProcess bpmBusinessProcess = this.mapper.selectOne(new QueryWrapper<BpmBusinessProcess>().eq("BUSINESS_NUMBER", processNumber));
        bpmBusinessProcess.setIsDel(1);
        this.updateById(bpmBusinessProcess);
    }

	/**
	 * 查询审批人为空时的处理策略信息
	 * @param processNumber 流程编号
	 * @return 节点审批人为空的动作信息
	 */
	public NodeHeadActionDto getNodeIdApproverIdByProcessNumber(String processNumber) {
		if (StringUtils.isBlank(processNumber)) {
			return null;
		}

		NodeHeadActionRunTaskDto runTaskInfoBuilder = this.mapper.getActRunTaskInfoByProcessNumber(processNumber);
		if (runTaskInfoBuilder == null) {
			return null;
		}
		BpmVariable bpmVariable = bpmVariableService.getBaseMapper().selectOne(new QueryWrapper<BpmVariable>().eq("process_num", processNumber));
		if (ObjectUtils.isEmpty(bpmVariable)) {
			return null;
		}
		// key: nextElementId value: BaseIdTranStruVo
		Map<String, List<BaseIdTranStruVo>> nodeApproveds = bpmVerifyInfoBizService.getNodeApproveds(bpmVariable.getId());

		// 获取下个节点的审批人id
		NodeHeadActionDto headAction = this.mapper.getHeadAction(processNumber, runTaskInfoBuilder.getTaskDefKey());
		if (headAction == null) { // 先查t_bpm_variable_multiplayer，若没有再去查询t_bpm_variable_single
			headAction = this.mapper.getHeadActionSingle(processNumber, runTaskInfoBuilder.getTaskDefKey());
			if (headAction == null) {
				return null;
			}
		}
		HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery().processInstanceId(runTaskInfoBuilder.getProcInstId()).singleResult();
		List<ActivityImpl> activitiList = activitiAdditionalInfoService.getActivitiList(historicProcessInstance);
		List<HistoricTaskInstance> historics = historyService.createHistoricTaskInstanceQuery().processInstanceId(historicProcessInstance.getId()).orderByHistoricTaskInstanceEndTime().desc().list();
		HistoricTaskInstance lastHistoricTaskInstance = historics.get(0);
		List<PvmActivity> nextElements = activitiAdditionalInfoService.getNextElementList(lastHistoricTaskInstance.getTaskDefinitionKey(), activitiList);
		if (CollectionUtils.isEmpty(nextElements)) {
			log.warn("没有nextElements");
			return null;
		}
		Map<String, String> userMap = new HashMap<>();
		for (PvmActivity nextElement : nextElements) {
			// get next node's approvers
			List<BaseIdTranStruVo> baseIdTranStruVos = nodeApproveds.get(nextElement.getId());
			if (!ObjectUtils.isEmpty(baseIdTranStruVos)) {
				for (BaseIdTranStruVo empBaseInfo : baseIdTranStruVos) {
					userMap.put(empBaseInfo.getId(), empBaseInfo.getName());
				}
			}
		}
		headAction.setUserMap(userMap);
		headAction.setTaskId(runTaskInfoBuilder.getTaskId());
		return headAction;
	}

	/**
	 * 手动让流程终结，并且删除掉此流程 （仅用于删除系统中的无效流程）
	 */
	public void makeProcessEnd(MakeProcessEndReqVo req) {
		if (CollectionUtils.isEmpty(req.getProcessNumberList()) && CollectionUtils.isEmpty(req.getBussinessList())) {
			return;
		}
		List<String> businessIdList = new ArrayList<>();
		if (CollectionUtils.isEmpty(req.getBussinessList())) {
			List<BpmBusinessProcess> list = this.lambdaQuery().select(BpmBusinessProcess::getBusinessId).eq(BpmBusinessProcess::getBusinessNumber, req.getProcessNumberList()).list();
			if (!CollectionUtils.isEmpty(list)) {
				businessIdList = Lists.transform(list, BpmBusinessProcess::getBusinessId);
			}
		} else {
			businessIdList = req.getBussinessList();
		}
		log.warn("操作人：{}, 手动让流程终结，并且删除掉此流程 （仅用于删除系统中的无效流程）, businessIdList:{}", SecurityUtils.getLogInEmpId(), businessIdList);
		this.mapper.makeProcessEnd(businessIdList);
	}

	@Override
	public void updateMergedUserInfoHistory(List<String> historyUserIds, String targetUserId) {
		if (CollectionUtils.isEmpty(historyUserIds) || StringUtils.isBlank(targetUserId)) {
			return;
		}
		mapper.updateMergedUserInfoHistory(historyUserIds, targetUserId);
		mapper.updateMergedUserInfoRuTask(historyUserIds, targetUserId);
		mapper.updateMergedUserInfoHiTask(historyUserIds, targetUserId);
		mapper.updateMergedUserInfoForward(historyUserIds, targetUserId);
	}

    public BpmBusinessProcess getProcessByProcessNumber(String processNumber) {
        return mapper.getProcessByProcessNumber(processNumber);
    }
    @Override
    public void exportToExcel(HttpServletResponse response, BpmBusinessProcessExportVo vo) throws Exception {
	    /**
	     * TODO
	     * 创建人、机构 可以同时没有，此时只可以导出最近两年范围内的 数据（如果该formcode的审批最早的一条在最近2年外，则报错提示）
	     */

	    if (!Objects.isNull(vo.getEmpInfo()) && !Objects.isNull(vo.getOrgInfo())
		    && !vo.getEmpInfo().getEhrSource().equals(vo.getOrgInfo().getEhrSource())) {
		    log.error("员工所属组织和机构所属组织不一致");
		    throw new RuntimeException("员工所属组织和机构所属组织不一致");
	    }

	    BpmnConf bpmnConf = bpmnConfMapper.getByFormCode(vo.getFormCode());
	    String fileName = bpmnConf.getBpmnName();

	    // 获取员工id列表
	    Set<String> userIds = new HashSet<>();
	    if (!Objects.isNull(vo.getOrgInfo())) {
		    userIds = userService.getUserIdsByOrgId(vo.getOrgInfo().getId(), vo.getOrgInfo().getEhrSource());
	    }

	    if (!Objects.isNull(vo.getEmpInfo())) {
		    if (!userIds.contains(vo.getEmpInfo().getId())) {
			    log.error("所选人员信息、组织信息冲突，请核查！");
			    throw new RuntimeException("所选人员信息、组织信息冲突，请核查！");
		    }
	    }

	    // 获取审批
	    LambdaQueryWrapper<BpmBusinessProcess> queryWrapper = new LambdaQueryWrapper<>();
	    queryWrapper.eq(BpmBusinessProcess::getProcessinessKey, vo.getFormCode());

	    if (!CollectionUtils.isEmpty(userIds)) {
		    queryWrapper.in(BpmBusinessProcess::getCreateUser, userIds);
	    }

	    if (!Objects.isNull(vo.getStartDate()) && !Objects.isNull(vo.getEndDate())) {
		    queryWrapper.between(BpmBusinessProcess::getCreateTime, vo.getStartDate(), vo.getEndDate());

		    String startDateStr = DateUtils.formatYMD10(vo.getStartDate());
		    String endDateStr = DateUtils.formatYMD10(vo.getEndDate());
		    fileName += "_" + startDateStr + "_" + endDateStr;
	    }

	    List<BpmBusinessProcess> bpmBusinessProcesses = mapper.selectList(queryWrapper);
	    HashMap<String, Object> commParams = new HashMap<String, Object>() {
		    {
			    put("formCode", vo.getFormCode());
			    put("type", 2);
			    put("isOutSideAccessProc", false);
			    put("isLowCodeFlow", true);
		    }
	    };

	    if(CollectionUtils.isEmpty(bpmBusinessProcesses)){
		    log.warn("该流程模版[{}]未发起任何审批！", vo.getFormCode());
		    throw new RuntimeException("该模版无审批流程！");
	    }

	    // 获取各个审批表单信息
	    List<PdfPublicFields> processFieldList = bpmBusinessProcesses.stream().map(bpmBusinessProcess -> {
		    commParams.put("processNumber", bpmBusinessProcess.getBusinessNumber());
		    return pdfService.getFieldValues(JSON.toJSONString(commParams));
	    }).collect(Collectors.toList());

	    // 获取动态字段列表
	    List<PdfPublicFields.ApprovalContent> approvalContents = processFieldList.get(0).getApprovalContents();
	    List<String> dynamicFieldList = approvalContents.stream()
		    .filter(f -> !"alert".equals(f.getType()))
		    .map(f -> f.getFieldId() + FIELD_NAME_ID_SEPARATOR + f.getFieldName()).collect(Collectors.toList());


	    // 获取员工的组织信息

	    if(CollectionUtils.isEmpty(userIds)){
		    userIds = bpmBusinessProcesses.stream().map(BpmBusinessProcess::getCreateUser).collect(Collectors.toSet());
	    }

	    List<User> users = userMapper.queryByUserIds(userIds);
	    Set<String> orgIds = users.stream().map(User::getOrgId).collect(Collectors.toSet());
	    List<SyncEhrOrgInfo> orgInfos = syncEhrOrgInfoService.list(new LambdaQueryWrapper<SyncEhrOrgInfo>().in(SyncEhrOrgInfo::getOrgId, orgIds));
	    Map<String, String> orgPathNameMap = userService.getNamePath(orgInfos, false);

	    List<List<String>> headList = assembleTableHeader(dynamicFieldList, bpmnConf.getSubmitUserFieldId());
	    List<Object> dataList = assembleTableBody(dynamicFieldList, processFieldList,orgPathNameMap);


	    if (Objects.isNull(vo.getStartDate())) {
		    Date minDate = null;
		    Date maxDate = null;
		    for (PdfPublicFields fields : processFieldList) {
			    Date submitTime = fields.getSubmitTime();
			    if (minDate == null || submitTime.before(minDate)) {
				    minDate = submitTime;
			    }

			    if (maxDate == null || submitTime.after(maxDate)) {
				    maxDate = submitTime;
			    }
		    }
		    ;
		    fileName += "_" + DateUtils.formatYMD10(minDate) + "_" + DateUtils.formatYMD10(maxDate);
	    }
	    doExport(response, headList, dataList, fileName);
    }

	private List<List<String>> assembleTableHeader(List<String> dynamicFieldList, String submitterFieldId){
		List<List<String>> headList = new ArrayList<>();
		// 组装表头信息
		headList.add(Collections.singletonList("审批编号"));
		headList.add(Collections.singletonList("提交时间"));
		headList.add(Collections.singletonList("完成时间"));
		headList.add(Collections.singletonList("提交人"));
		headList.add(Collections.singletonList("提交人所属部门"));
		for (String fieldName : dynamicFieldList) {
			if (fieldName.contains(FIELD_NAME_ID_SEPARATOR)){
				String fieldId = fieldName.split(FIELD_NAME_ID_SEPARATOR)[0];
				fieldName = fieldName.split(FIELD_NAME_ID_SEPARATOR)[1];
				if (Objects.equals(fieldId, submitterFieldId)) {
					fieldName = fieldName + "（申请主体人）";
				}
			}
			headList.add(Collections.singletonList(fieldName));
		}
		headList.add(Collections.singletonList("当前审批状态"));
		return headList;
	}

	private List<Object> assembleTableBody(List<String> dynamicFieldList,
		List<PdfPublicFields> processFieldList,
		Map<String, String> orgPathNameMap){
		// 组装数据
		return processFieldList.stream().map(fields -> {
			List<String> line = new ArrayList<>();
			// 公共字段
			line.add(fields.getApprovalNumber());
			line.add(toString(fields.getSubmitTime()));
			line.add(toString(fields.getEndTime()));
			line.add(fields.getEmpName());
			line.add(orgPathNameMap.get(fields.getDeptId()));

			// 动态属性
			Map<String, String> fieldValueMap = fields.getApprovalContents().stream()
				.collect(Collectors.toMap(f -> f.getFieldId() + FIELD_NAME_ID_SEPARATOR + f.getFieldName(), PdfPublicFields.ApprovalContent::getFieldValue));
			for (String name : dynamicFieldList) {
				line.add(fieldValueMap.getOrDefault(name, ""));
			}
			// 公共属性
			line.add(fields.getApprovalStatusName());
			return line;
		}).collect(Collectors.toList());
	}

	public void doExport(HttpServletResponse response, List<List<String>> headList, List<Object> dataList, String fileName) throws Exception {
		// 设置响应头
		response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
		response.setCharacterEncoding("utf-8");

		// 处理文件名中文问题
		String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()).replaceAll("\\+", "%20");
		response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + encodedFileName + ".xlsx");

		// 使用EasyExcel写入数据
		EasyExcel.write(response.getOutputStream())
			.sheet("审批清单")
			.head(headList)
			.doWrite(dataList);
		log.info("Excel导出成功，文件名: {}, 数据量: {}", fileName, dataList.size());
	}

	private static String toString(Object obj) {
		if (obj == null) {
			return "";
		}

		if (obj instanceof Date) {
			return DateUtils.format((Date) obj, "yyyy-MM-dd HH:mm:ss");
		}
		return obj.toString();
	}

	/**
	 * 流程催办
	 */
	public void submiterReminders(String businessId) {
		String reminderKey = "AntFlow:submiterReminders:" + businessId;
		String lastReminderTime = stringRedisTemplate.opsForValue().get(reminderKey);

		if (StringUtils.isNotBlank(lastReminderTime)) {
			long lastTime = Long.parseLong(lastReminderTime);
			long currentTime = System.currentTimeMillis();
			long interval = currentTime - lastTime;
			if (interval < 300000) {
				log.warn("流程催办频率过快, 距离上次催办时间不足5分钟, 流程编号: {}", businessId);
				throw new JiMuBizException("催办过于频繁，请5分钟后再试");
			}
		}

		BpmBusinessProcess bpmBusinessProcess = mapper.getByBusinessId(businessId);
		String startUserId = bpmBusinessProcess.getCreateUser();
		String processNumber = bpmBusinessProcess.getBusinessNumber();
		BpmnConf bpmnConf = bpmnConfService.lambdaQuery().select(BpmnConf::getFormCode).eq(BpmnConf::getId, bpmBusinessProcess.getTemplateId()).last("LIMIT 1").one();
		String procInstId = bpmBusinessProcess.getProcInstId();
		if (StringUtils.isBlank(procInstId)) {
			log.error("流程催办失败, 当前流程实例id,procInstId为空, businessId: {}", businessId);
			return;
		}
		List<BpmVerifyInfoVo> taskInfor = bpmVerifyInfoMapper.findTaskInfor(procInstId);
		if (CollectionUtils.isEmpty(taskInfor)) {
			throw new JiMuBizException("流程催办失败, 当前流程" + businessId + "，已经结束");
		}
		List<String> notifyUserIds = Lists.transform(taskInfor, BpmVerifyInfoVo::getVerifyUserId);
		log.info("流程催办, 接受消息的审核人: {}, businessId: {}", notifyUserIds, businessId);
		notifyService.notice(startUserId, bpmnConf.getFormCode(), businessId, notifyUserIds, NoticeMsgEnum.PROCESS_REMINDER, processNumber);
		stringRedisTemplate.opsForValue().set(reminderKey, String.valueOf(System.currentTimeMillis()), 300, TimeUnit.SECONDS);
	}
}