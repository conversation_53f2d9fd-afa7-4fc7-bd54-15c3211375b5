server: 
  port: 8080
spring:
  datasource:
    url: ********************************************************************************************************************************************************************************************************************************
    username: antflowuser
    password: wHA8qSiV4S$Es
    driver-class-name: com.mysql.cj.jdbc.Driver
    druid: 
      min-idle: 5
      initial-size: 5
      keep-alive: true
      max-wait: 60000
      max-active: 100
      removeAbandoned: true
      removeAbandonedTimeout: 1800
      logAbandoned: true
      validation-query: SELECT 1 FROM DUAL
      validation-query-timeout: 2000
      test-on-borrow: false
      test-on-return: false
      test-while-idle: true
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
    hikari: 
      max-lifetime: 120000
  activiti: 
    # logging.level.org.openoa.engine.conf.mvc.JiMuMDCCommonsRequestLoggingFilter=debug
    # activiti Disable Auto Table Creation
    database-schema-update: none
  redis:
    client-type: lettuce
    cluster:
      max-redirects: 8
      nodes: uc-hb2-scrm-rds-node1.online.local:7000,uc-hb2-scrm-rds-node2.online.local:7000,uc-hb2-scrm-rds-node3.online.local:7000,uc-hb2-scrm-rds-node4.online.local:7000,uc-hb2-scrm-rds-node5.online.local:7000,uc-hb2-scrm-rds-node6.online.local:7000
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        max-wait: -1
        min-idle: 0
    password: dfdr3r44DwsdCK
    timeout: 5000

  ## 多租户数据源,需要时开启
  #spring.antflow.tenanta.url=jdbc: mysql: //localhost: 3306/tenanta
  #spring.antflow.tenanta.username=root
  #spring.antflow.tenanta.password=123456
  #spring.antflow.tenantb.url=jdbc: mysql: //localhost: 3306/tenanta
  #spring.antflow.tenantb.username=root
  #spring.antflow.tenantb.password=123456

mybatis: 
  configuration: 
    ## mybatis
    map-underscore-to-camel-case: true
  type-aliases-package: org.openoa.**.entity
#mybatis-plus:
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
logging: 
  level: 
    org: 
      openoa: 
        mapper: info
      activiti: 
        engine: 
          impl: 
            persistence: 
              entity: info
xxl: 
  job:
    accessToken: 
    admin: 
      addresses: http://bj-zw-xds-ds-api-omo.online.local/xxl-job-admin
    executor: 
      appname: antflow-release
      address: 
      ip:
      port: 9421
      logpath: logs/xxl-job/jobhandler
      logretentiondays: 3
      oneTimesJob: 
        timeout: 10000
file:
  upload:
    dir: /usr/ftp_home/cesupport
    url: https://cesupport-images.ceboss.cn
gateway:
  flowApi:
    url: https://cescrm.ceboss.cn/antflow
master-data:
  domain: http://masterdata.300ce.cn/api
wechat:
  chenxing:
    agentId: 1000110
    corpId: wwe2351ed80f0479d4
    corpsecret: fqGRcqIxTCe0vloAIszDkVkJ9eRwBGPkd8UqpuhLNT4
    approveDomain: approve.oristarcloud.com
  jituan:
    agentId: 1000007
    corpId: wwa0a360c6341c5ae6
    corpsecret: rzCrBz5DiORPI4upTSaDlh9_oTg0Ti0KynbbdaA3iA8
    approveDomain: approve.ce-group.cn
  kuajing:
    agentId: 1000059
    corpId: ww8bd429f5b618a57e
    corpsecret: jXeOMq9AIRvrGt2saBHqspjyvgeMcg9UshkLBRlScQk
    approveDomain: approve.gboss.tech
  smhg:
    agentId: 999999
    corpId: 999999
    corpsecret: 999999
    approveDomain: 999999
  xiaoxia:
    agentId: 1000012
    corpId: ww71746e109858902c
    corpsecret: JTXoKPILcBhK3oBAGqlWdteh0ojjhNd7clbyMDfr9oA
    approveDomain: approve.xiaoxiatech.com
  xinwang:
    agentId: 1000128
    corpId: wwd674e2ffdc4f6862
    corpsecret: OR0EVcctcYO0PtQuiTkWJJWW8wWzn3HFrhVLZ1Y_VqI
    approveDomain: approve.xinnet.com
  zhongqi:
    agentId: 1000228
    corpId: wwf1c477c862181a7f
    corpsecret: Q4oqN8Vdo8jZTCragwteb2g8EqVzkNXjMNfaqIk-jVg
    approveDomain: approve.ceboss.cn
# error日志告警通知的企微群链接地址
alarmAddress: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=a847ea54-6773-42c0-8ba3-e673f4d36f8d

wx_redirect_url: https://open.weixin.qq.com/connect/oauth2/authorize?appid={corpId}&redirect_uri={redirectUrl}&response_type=code&state=approvalProcess&scope=snsapi_base#wechat_redirect