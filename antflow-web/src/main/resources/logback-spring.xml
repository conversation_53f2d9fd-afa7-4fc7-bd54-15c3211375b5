<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <conversionRule conversionWord="mx" converterClass="com.sto.trackingapi.configs.logconfig.CustomMessageConverter" />
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    <property name="logPath" value="/data/appcenter/logs/antflow-web"/>
    <property name="APP_NAME" value="antflow"/>
    <property name="LOG_LEVEL" value="INFO"/>
    <property name="user.home" value="log"/>
    <property name="file_log_pattern" value="%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level [%t] %C.%M:%L - [traceId:%X{traceId}] %m%n"/>
<!--    <property name="LOG_ROOT_PATH" value="/data/appcenter/logs/antflow-web"/>-->
    <property name="MW_LOG_PATH" value="${LOG_ROOT_PATH}/logs"/>
    <property name="no_file_pattern"
              value="%boldGreen(%d{MM-dd HH:mm:ss.SSS}) %level %highlight([%X{ruid}]) [%thread] %cyan(%c) %blue([%file:%line]) %msg%n"/>
    <property name="file_pattern"
              value="%boldGreen(%d{MM-dd HH:mm:ss.SSS}) %level %highlight([%X{ruid}]) [%thread] %c [%file:%line] %msg%n"/>


    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${file_log_pattern}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <appender name="fileAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_ROOT_PATH}/antflow-web.log</file>
        <filter class="com.nrcp.log.ErrorLogFilter"/>
        <!--滚动策略-->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!--路径-->
            <fileNamePattern>${logPath}/antflow.log.%d{yyyy-MM-dd}.%i</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>100MB</maxFileSize>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>

        <encoder>
            <pattern>${file_log_pattern}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

        <logger name="org.openoa.AntFlowApplication" level="${LOG_LEVEL}" additivity="false">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="fileAppender"/>
        </logger>
        <root level="${LOG_LEVEL}">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="fileAppender"/>
        </root>
        <logger name="com.alibaba" level="error" additivity="false"/>
</configuration>