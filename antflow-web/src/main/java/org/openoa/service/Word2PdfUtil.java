package org.openoa.service;

import com.aspose.cells.Workbook;
import com.aspose.words.Document;
import com.aspose.words.License;
import com.aspose.words.SaveFormat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.FileOutputStream;
import java.io.InputStream;

/**
 * 使用Aspose将Word转换为PDF的工具类
 * 注意：需要在resource路径下配置license.xml文件
 */
public class Word2PdfUtil {

    private static Logger log = LoggerFactory.getLogger(Word2PdfUtil.class);


    /**
     * 将Word文档转换为PDF
     *
     * @param inPath  输入的Word文件路径
     * @param outPath 输出的PDF文件路径
     */
    public static void doc2pdf(String inPath, String outPath) throws Exception {
        // 如果未获取到许可证，则抛出业务异常
        if (!getLicense()) {
            log.error("将Word文档转换为PDF,未配置许可证!");
            return;
        }
        try (FileOutputStream os = new FileOutputStream(outPath)) {
            long startTime = System.currentTimeMillis();

            // 要转换的Word文档
            Document doc = new Document(inPath);
            // 接受所有修订记录
            doc.acceptAllRevisions();
            // 保存为PDF格式
            doc.save(os, SaveFormat.PDF);

            log.info("PDF转换完成，耗时：{}ms,输出路径:{}", System.currentTimeMillis() - startTime, outPath);
        } catch (Exception e) {
            log.error("将Word文档转换为PDF过程发生异常", e);
        }
    }

    /***
     *  将Excel转换为PDF
     * @param inExcelPath excel文件地址
     * @param outPath 输出地址
     * <AUTHOR>
     * @date 2025/9/8 16:36
     * @version 1.0.0
     * @return void
    **/
    public static void excel2pdf(String inExcelPath, String outPath) throws Exception {
        // 如果未获取到许可证，则抛出业务异常
        if (!getLicense()) {
            log.error("将Excel转换为PDF,未配置许可证!");
            return;
        }
        try (FileOutputStream os = new FileOutputStream(outPath)) {
            long startTime = System.currentTimeMillis();

            Workbook workbook = new Workbook(inExcelPath);

            // 保存为 PDF
            workbook.save(outPath, com.aspose.cells.SaveFormat.PDF);
//            workbook.save(outPath, com.aspose.cells.SaveFormat.HTML);

            log.info("PDF转换完成，耗时：{}ms,输出路径:{}", System.currentTimeMillis() - startTime, outPath);
        } catch (Exception e) {
            log.error("将Excel转换为PDF过程发生异常 inExcelPath={}", inExcelPath, e);
        }
    }

    /**
     * 获取Aspose的许可证
     * 如果未获取则转换的pdf带有水印
     *
     * @return 是否成功获取许可证
     */
    public static boolean getLicense() {
        boolean result = false;
        // license.xml文件应放在resource路径下
        try (InputStream is = Word2PdfUtil.class.getClassLoader().getResourceAsStream("license.xml");) {
            License license = new License();
            license.setLicense(is);
            result = true;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
//        return true;
    }

    public static void main(String[] args) throws Exception {
        System.out.println(getLicense());
//        doc2pdf("/Users/<USER>/Desktop/hlh/下发服务单+-crm_合同.docx", "/Users/<USER>/Desktop/hlh/下发服务单+-crm_合同.pdf");
//        excel2pdf("/Users/<USER>/Desktop/hlh/48D48B12C02B4A0CAC96DBCC4C78D7B8.xlsx", "/Users/<USER>/Desktop/hlh/test_excel.pdf");
        excel2pdf("/Users/<USER>/Desktop/hlh/48D48B12C02B4A0CAC96DBCC4C78D7B8.xlsx", "/Users/<USER>/Desktop/hlh/test_excel_html.html");
    }
}
