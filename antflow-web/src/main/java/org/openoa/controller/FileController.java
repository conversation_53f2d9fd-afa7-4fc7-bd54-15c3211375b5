package org.openoa.controller;

import lombok.extern.slf4j.Slf4j;
import org.openoa.base.entity.Result;
import org.openoa.base.exception.JiMuBizException;
import org.openoa.engine.utils.FileUploadUtil;
import org.openoa.vo.resp.FileUploadResp;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

/**
 * 标签复用的Role的逻辑，所以实际操作的是role数据
 */
@RequestMapping("/file")
@RestController
@Slf4j
public class FileController {


	@Value("${file.upload.dir}")
	private String uploadDir;

	@Value("${file.upload.url}")
	private String uploadUrl;

    /**
     * 上传文件
     */
    @PostMapping("/upload")
    public Result<FileUploadResp> upload(MultipartFile file) {
		if (file.isEmpty()) {
			throw new JiMuBizException("上传文件不能为空");
		}
	    SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
	    String format = sdf.format(new Date());
	    String filePath = "/antFlow/" + format + "/";
	    Map<String, String> rs;
	    try {
		    rs = FileUploadUtil.fileUpload(file, uploadDir + filePath);
	    } catch (Exception e) {
		    return Result.newFailureResult(e.getMessage());
	    }
		String url = uploadUrl + filePath + rs.get("fileName");
		log.info("文件上传成功，url={}", url);
	    return Result.newSuccessResult(FileUploadResp.builder().name(rs.get("oriFileName")).url(url).build());
    }
}
