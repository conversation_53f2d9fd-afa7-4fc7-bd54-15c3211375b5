package org.openoa.controller;

import lombok.extern.slf4j.Slf4j;
import org.openoa.base.entity.Result;
import org.openoa.service.Word2PdfUtil;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.UUID;

/**
 * @Description DOC转PDF控制器
 * <AUTHOR> @Date 2025-09-08
 */
@Slf4j
@RestController
@RequestMapping("/docToPdf")
public class DocToPdfController {
    
    /**
     * 将DOC文件转换为PDF文件
     * @param docUrl DOC文件URL地址
     * @return PDF文件URL地址
     */
    @PostMapping("/convert")
    public Result<String> convertDocToPdf(@RequestParam("docUrl") String docUrl) {
        String tempDir = System.getProperty("java.io.tmpdir");
        String docFilePath = null;
        String pdfFilePath = null;
        
        try {
            // 校验输入参数
            if (docUrl == null || docUrl.trim().isEmpty()) {
                return Result.newFailureResult("DOC文件地址不能为空");
            }
            
            // 检查是否是doc文件地址
            if (!docUrl.toLowerCase().endsWith(".doc") && !docUrl.toLowerCase().endsWith(".docx")) {
                return Result.newFailureResult("文件格式不正确，仅支持doc/docx格式");
            }
            
            // 构造PDF文件URL地址
            String pdfUrl = docUrl.substring(0, docUrl.lastIndexOf(".")) + ".pdf";
            
            // 下载doc文件到临时目录
            String fileName = docUrl.substring(docUrl.lastIndexOf("/") + 1);
            docFilePath = tempDir + File.separator + fileName;
            pdfFilePath = tempDir + File.separator + UUID.randomUUID() + ".pdf";
            
            // 下载文件
            downloadFile(docUrl, docFilePath);
            
            // 调用转换工具进行转换
            Word2PdfUtil.doc2pdf(docFilePath, pdfFilePath);
            
            // TODO: 实际项目中需要实现文件上传逻辑，将pdf文件上传到文件服务器
            // 这里简化处理，直接返回构造的URL
            
            return Result.newSuccessResult(pdfUrl);
        } catch (Exception e) {
            log.error("DOC转PDF失败，docUrl:{}", docUrl, e);
            return Result.newFailureResult("DOC转PDF失败:" + e.getMessage());
        } finally {
            // 清理临时文件
            try {
                if (docFilePath != null && Files.exists(Paths.get(docFilePath))) {
                    Files.delete(Paths.get(docFilePath));
                }
                if (pdfFilePath != null && Files.exists(Paths.get(pdfFilePath))) {
                    Files.delete(Paths.get(pdfFilePath));
                }
            } catch (Exception e) {
                log.warn("清理临时文件失败", e);
            }
        }
    }
    
    /**
     * 从URL下载文件
     * @param fileUrl 文件URL
     * @param filePath 保存路径
     * @throws IOException IO异常
     */
    private void downloadFile(String fileUrl, String filePath) throws IOException {
        URL url = new URL(fileUrl);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("GET");
        connection.setConnectTimeout(5000);
        connection.setReadTimeout(10000);
        
        try (InputStream inputStream = connection.getInputStream();
             FileOutputStream outputStream = new FileOutputStream(filePath)) {
            
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            outputStream.flush();
        }
    }
}