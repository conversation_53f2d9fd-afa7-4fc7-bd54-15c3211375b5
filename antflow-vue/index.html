<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="renderer" content="webkit">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
  <link rel="icon" href="/favicon.ico">
  <title>审批系统</title>
  <script>
    // 捕获错误
    window.onerror = function (message, source, lineno, colno, error) {
      console.error('捕获到的错误:', message);
      if(message && message.includes("Failed to load module script")) {
        const result = confirm('页面功能有更新，为避免影响您的使用，请点击确定立即刷新页面或点击“取消”自己手动刷新')
        result && location.reload()
        return true;
      }
      console.error('来源:', source);
      console.error('行号:', lineno);
      console.error('列号:', colno);
      console.error('错误详情:', error);
    };
  </script>
  <script type="text/javascript" src="/env.js?v=0.0.0"></script>
</head>

<body>
  <div id="app"></div>
  <script type="module" src="/src/main.js"></script>
</body>

</html>