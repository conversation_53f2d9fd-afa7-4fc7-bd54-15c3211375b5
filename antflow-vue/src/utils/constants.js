export const APPROVED_STATUS = [
  { label: '审批中', value: '1' },
  { label: '审批通过', value: '2' },
  { label: '审批拒绝', value: '6' }
]
// 动态生成 MAP
export const APPROVED_STATUS_MAP = APPROVED_STATUS.reduce((map, item) => {
  map[item.value] = item.label
  return map
}, {})

// 模板状态
export const TEMPLATE_STATUS = [
  { label: '启用中', value: 0 },
  { label: '已禁用', value: 1 },
]

export const dateShortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setDate(start.getDate() - 7)
      return [start, end]
    },
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setMonth(start.getMonth() - 1)
      return [start, end]
    },
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setMonth(start.getMonth() - 3)
      return [start, end]
    },
  },
]

export const EHR_SOURCE = [
  { label: '集团', value: '0_2654', type: 2, ehrSource: '0' },
  { label: '中企', value: '1_9', type: 2, ehrSource: '1' },
  { label: '跨境', value: '2_9', type: 2, ehrSource: '2' },
  { label: '新网', value: '3_1', type: 2, ehrSource: '3' },
  { label: '辰星', value: '4_1', type: 2, ehrSource: '4' },
  { label: '数码慧谷', value: '5_smhg0745528d9dd246078addbc4df01fbb0d', type: 2, ehrSource: '5' },
  { label: '小侠', value: '6_3042', type: 2, ehrSource: '6' },
]

export const wecomCorpListOnline = [
  {
    corpid: "wwf1c477c862181a7f",
    corp_name: "中企动力",
    agentid: '1000228',
    domainUrl: `//approve.ceboss.cn`
  },
  {
    corpid: "wwa0a360c6341c5ae6",
    corp_name: "中企集团",
    agentid: '1000007',
    domainUrl: `//approve.ce-group.cn`
  },
  {
    corpid: "wwd674e2ffdc4f6862",
    corp_name: "新网数码",
    agentid: '1000128',
    domainUrl: `//approve.xinnet.com`
  },
  {
    corpid: "ww8bd429f5b618a57e",
    corp_name: "中企跨境",
    agentid: '1000059',
    domainUrl: `//approve.gboss.tech`
  },
  {
    corpid: "wwe2351ed80f0479d4",
    corp_name: "新辰星科技",
    agentid: '1000110',
    domainUrl: `//approve.oristarcloud.com`
  },
  {
    corpid: "ww71746e109858902c",
    corp_name: "小侠",
    agentid: '1000012',
    domainUrl: `//approve.xiaoxiatech.com`
  },
]

export const wecomCorpListTest = [
  {
    corpid: "wwf1c477c862181a7f",
    corp_name: "中企动力",
    agentid: '1000229',
    domainUrl: `//test-approve.ceboss.cn`
  },
  {
    corpid: "wwa0a360c6341c5ae6",
    corp_name: "中企集团",
    agentid: '1000008',
    domainUrl: `//test-approve.ce-group.cn`
  },
  {
    corpid: "wwd674e2ffdc4f6862",
    corp_name: "新网数码",
    agentid: '1000129',
    domainUrl: `//test-approve.xinnet.com`
  },
  {
    corpid: "ww8bd429f5b618a57e",
    corp_name: "中企跨境",
    agentid: '1000060',
    domainUrl: `//test-approve.gboss.tech`
  },
  {
    corpid: "wwe2351ed80f0479d4",
    corp_name: "新辰星科技",
    agentid: '1000112',
    domainUrl: `//test-approve.oristarcloud.com`
  },
  {
    corpid: "ww71746e109858902c",
    corp_name: "小侠",
    agentid: '1000011',
    domainUrl: `//test-approve.xiaoxiatech.com`
  }
]

//判断当前入口是PC端还是APP端
export const isMobile = navigator.userAgent.match(
  /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i
);
