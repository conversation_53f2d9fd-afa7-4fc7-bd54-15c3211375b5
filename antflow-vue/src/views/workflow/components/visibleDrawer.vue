<template>
  <div>
    <el-drawer v-model="showDrawer" :before-close="closeDrawer" :size="800" :with-header="false" destroy-on-close>
      <span style="font-weight: bold;">可见范围</span>
      <el-divider />
      <el-card>
        <template #header>
          <span>模版管理员</span>
          <span class="header-tips">（管理模板&查看申请记录）</span>
        </template>
        <MemberOrg type="1" v-model:dataList="pageData.adminInfo" el-key="templateAdmin" />
      </el-card>

      <el-card class="mt20" header="可见范围">
        <template #header>
          <span>可见范围</span>
          <span class="header-tips">（使用模板提交申请）</span>
        </template>
        <MemberOrg v-if="pageData.showOrgMember" type="3" v-model:dataList="pageData.visibleRange" :selectProps="{ checkStrictly: true }" el-key="formApply" />
      </el-card>
      <div class="mt20">
        <el-button type="primary" @click="saveVisible" :loading="pageData.saveLoading">保存</el-button>
        <el-button @click="closeDrawer">取消</el-button>
      </div>
    </el-drawer>
  </div>

</template>

<script setup>
import { reactive } from 'vue'
import MemberOrg from "@/components/MemberOrg/index.vue"
import { getVisibleRange, setVisibleRange } from '@/api/workflow'

const { proxy } = getCurrentInstance()
const emit = defineEmits(['update:visible'])
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  row: {
    type: Object,
    default: null
  }
})
let showDrawer = computed({
  get() {
    return props.visible
  },
  set() {
    closeDrawer()
  }
})
let pageData = reactive({
  showOrgMember: false,
  adminInfo: [], // 管理员信息
  visibleRange: [],
  saveLoading: false
})
// 保存可见范围
const saveVisible = () => {
  let confId = props.row?.id
  pageData.saveLoading = true
  confId && setVisibleRange({ confId, bpmnConfUserPermVo: pageData.visibleRange }).then(() => {
    proxy.$modal.msgSuccess('保存成功')
    closeDrawer()
  }).finally(() => {
    pageData.saveLoading = false
  })
}

// 获取可见范围
const getVisible = () => {
  let confId = props.row?.id
  confId && getVisibleRange({ confId }).then(res => {
    pageData.visibleRange = (res?.data || []).map(item => {
      return {
        id: item.sourceId,
        controlDataType: item.controlDataType,
        name: item.sourceName,
        ehrSource: item.ehrSource
      }
    })
  }).finally(() => {
    pageData.showOrgMember = true
  })
}

// 关闭抽屉
const closeDrawer = () => {
  emit('update:visible', false)
}

getVisible()
</script>

<style lang="scss" scoped>
  .header-tips {
    font-size: 12px;
    font-weight: 600;
    color: #f00;
  }
</style>