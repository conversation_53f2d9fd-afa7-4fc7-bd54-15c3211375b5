<template>
  <div class="app-container">
     <div class="query-box">
        <el-form :model="taskMgmtVO" ref="queryRef" inline label-width="85px">
           <el-form-item label="审批编号" prop="businessId">
              <el-input v-model="taskMgmtVO.businessId" placeholder="请输入审批编号" clearable style="width: 200px"
                 @keyup.enter="handleQuery" />
           </el-form-item>
           <el-form-item label="审批状态" prop="processState">
              <el-select v-model="taskMgmtVO.processState" placeholder="请选择审批状态" clearable style="width: 200px">
                 <el-option
                    v-for="item in pageData.statusOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                 />
              </el-select>
           </el-form-item>
           <el-form-item label="模板名称" prop="templateName" v-if="!formCode">
              <el-input v-model="taskMgmtVO.templateName" placeholder="请输入模板名称" clearable style="width: 200px"
                 @keyup.enter="handleQuery" />
           </el-form-item>
            <el-form-item label="提交时间">
               <el-date-picker
               v-model="pageData.filterForm.createTime"
               type="datetimerange"
               :shortcuts="dateShortcuts"
               range-separator="至"
               start-placeholder="开始日期"
               end-placeholder="结束日期"
               :default-time="[
                  new Date(2000, 1, 1, 0, 0, 0),
                  new Date(2000, 2, 1, 23, 59, 59),
               ]"
               align="right"
               :disabled-date="disabledDate"
               value-format="YYYY-MM-DD HH:mm:ss"
               style="width: 400px;">
               </el-date-picker>
            </el-form-item>
            <el-form-item label="提交人">
               <MemberOrg ref="empRef" type="1" v-model:dataList="pageData.filterForm.empInfo" :selectProps="{ multiple: false }" style="width: 400px;" />
            </el-form-item>
            <el-form-item label="提交人部门">
               <MemberOrg ref="orgRef" type="2" v-model:dataList="pageData.filterForm.orgInfo" :selectProps="{ multiple: false }" style="width: 400px;" />
            </el-form-item>
           <el-form-item>
              <el-button type="primary" @click="handleQuery">搜索</el-button>
              <el-button @click="resetQuery">重置</el-button>
           </el-form-item>
        </el-form>
        <div v-if="formCode">
          <el-button type="primary" plain @click="handleExport" :loading="pageData.saveLoading">导出</el-button>
        </div>
     </div>
     <div class="table-box">
        <el-table v-loading="loading" :data="dataList">
            <el-table-column label="审批编号" align="center" prop="businessId" />
            <el-table-column label="模板名称" align="center" prop="templateName" />
            <el-table-column label="模板分组" align="center" prop="templateGroupName" />
            <el-table-column label="模版创建人" align="center" prop="createUser" width="90" />
            <el-table-column label="状态" align="center" prop="processState" width="80">
               <template #default="scope">
                  <span>{{ APPROVED_STATUS_MAP[scope.row.processState] }}</span>
               </template>
            </el-table-column>
            <el-table-column label="提交人" align="center" prop="createUser" width="70" />
            <!-- todo: 提交人部门 -->
            <el-table-column label="提交人部门" align="center" prop="subOrgName" />
            <el-table-column label="提交时间" align="center" prop="createTime" />
            <el-table-column label="姓名" align="center" prop="submitUserName" width="70" />
            <el-table-column label="更新时间" align="center" prop="updateTime" />
           <el-table-column label="操作" align="center" fixed="right">
              <template #default="scope">
                 <el-button link type="primary" @click="handlePreview(scope.row)">查看</el-button>
                  <el-button :loading="scope.row.downloadPdfLoading" link type="primary" @click="handleDownloadPDF(scope.row)">下载审批单</el-button>
              </template>
           </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" v-model:page="pageDto.page" v-model:limit="pageDto.pageSize"
           @pagination="getList" />
        <previewDrawer v-if="visible" />
     </div>
  </div>

</template>

<script setup>
import { getRequestlistPage, downloadPdf, exportExcel } from "@/api/workflow";
import previewDrawer from "@/views/workflow/components/previewDrawer.vue";
import { useStore } from '@/store/modules/workflow';
import { APPROVED_STATUS, APPROVED_STATUS_MAP, dateShortcuts } from '@/utils/constants';
import MemberOrg from '@/components/MemberOrg/index.vue'

const { proxy } = getCurrentInstance();
let store = useStore();
let { setPreviewDrawer, setPreviewDrawerConfig } = store;
let previewDrawerVisible = computed(() => store.previewDrawer);
const dataList = ref([]);
const loading = ref(true);
const total = ref(0);
const route = useRoute();
const formCode = computed(() => route.query.formCode || '');
const empRef = ref()
const orgRef = ref()
let visible = computed({
  get() {
     return previewDrawerVisible.value
  },
  set() {
     closeDrawer()
  }
});

const pageData = reactive({
  form: {},
  pageDto: {
     page: 1,
     pageSize: 10
  },
  taskMgmtVO: {
     businessId: undefined,
     templateName: undefined,
     processState: null
  },
  filterForm: {
    createTime: '',
    empInfo: [],
    orgInfo: []
  },
  statusOptions: APPROVED_STATUS,
  saveLoading: false
});
const { pageDto, taskMgmtVO } = toRefs(pageData);

/** 查询岗位列表 */
async function getList() {
  loading.value = true;
  
  // 构建查询参数
  const queryParams = { ...taskMgmtVO.value };
  
   // 处理时间范围
   if (pageData.filterForm.createTime && pageData.filterForm.createTime.length === 2) {
   queryParams.startDate = pageData.filterForm.createTime[0];
   queryParams.endDate = pageData.filterForm.createTime[1];
   }
   
   // 处理提交人
   if (pageData.filterForm.empInfo && pageData.filterForm.empInfo.length > 0) {
   queryParams.empInfo = pageData.filterForm.empInfo[0] || null;
   }
   
   // 处理提交人部门
   if (pageData.filterForm.orgInfo && pageData.filterForm.orgInfo.length > 0) {
   queryParams.orgInfo = pageData.filterForm.orgInfo[0] || null;
   }
   
   // 添加formCode
   queryParams.formCode = formCode.value;
  
  await getRequestlistPage(pageDto.value, queryParams, 6).then(response => {
     //console.log('response=========',JSON.stringify(response));
     dataList.value = response.data;
     total.value = response.pagination.totalCount;
     loading.value = false;
  }).catch((r) => {
     loading.value = false;
     console.log(r);
     proxy.$modal.msgError("加载列表失败:" + r.message);
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  pageDto.value.page = 1;
  getList();
}

function resetQuery() {
   taskMgmtVO.value = {
      businessId: undefined,
      templateName: undefined,
      processState: null
   };
   
   // 重置额外筛选条件
   pageData.filterForm = {
      createTime: '',
      empInfo: [],
      orgInfo: []
   };
   empRef.value.clearMemberOrg()
   orgRef.value.clearMemberOrg()
   handleQuery();
}

// 限制日期选择范围，只能选择近2年内的时间（今天及之前2年）
const disabledDate = (time) => {
  const today = new Date()
  // 计算2年前的日期
  const twoYearsAgo = new Date(today)
  twoYearsAgo.setFullYear(today.getFullYear() - 2)
  
  // 限制只能选择今天及之前2年的日期
  return time.getTime() > today.getTime() || time.getTime() < twoYearsAgo.getTime()
}

function handlePreview(row) {
  setPreviewDrawer(true);
  setPreviewDrawerConfig({
     formCode: row.processKey,
     processNumber: row.processNumber,
     isOutSideAccess: row.isOutSideProcess,
     isLowCodeFlow: row.isLowCodeFlow,
  })
}

// 下载pdf
const handleDownloadPDF = (row) => {
   let params = {
      formCode: row.processKey,
      processNumber: row.processNumber,
      type: 2,
      isOutSideAccessProc: false,
      isLowCodeFlow: true
   }
   row.downloadPdfLoading = true
   downloadPdf(params).then(res => {
      console.log(res);
      // 获取二进制数据
      const blob = res

      // 创建下载链接
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${row.description || '审批单'}.pdf`; // 设置下载文件名
      document.body.appendChild(a);
      a.click();

      // 清理
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
   }).finally(() => {
      row.downloadPdfLoading = false;
   })
}

// 导出
const handleExport = () => {
  let params = {
    formCode: formCode.value,
    businessId: taskMgmtVO.value.businessId,
    processState: taskMgmtVO.value.processState,
    empInfo: pageData.filterForm.empInfo[0] || null,
    orgInfo: pageData.filterForm.orgInfo[0] || null
  }
  
  // 处理时间范围
  if (pageData.filterForm.createTime && pageData.filterForm.createTime.length === 2) {
    params.startDate = pageData.filterForm.createTime[0]
    params.endDate = pageData.filterForm.createTime[1]
  }
  
  pageData.saveLoading = true
  exportExcel(params).then((res) => {
    // 获取二进制数据
    const blob = new Blob([res], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    
    // 创建下载链接
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    let downloadName = params.startDate && params.endDate ? `（${params.startDate}至${params.endDate}）` : '';
    downloadName = ('申请记录' || '模版设计') + downloadName;
    a.download = `${downloadName}.xlsx`; // 设置下载文件名
    document.body.appendChild(a);
    a.click();
    
    // 清理
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
    
    proxy.$modal.msgSuccess('导出成功')
  }).finally(() => {
    pageData.saveLoading = false
  })
}

getList();
</script>