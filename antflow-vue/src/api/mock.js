/*
 * @Date:  2024-05-25 14:06:59
 * @LastEditors: LDH <EMAIL>
 * @LastEditTime: 2024-06-29 15:52:57
 * @FilePath: src\api\mock.js
 * 静态数据模拟接口
 */
import http from '@/utils/axios'
let baseURL = import.meta.env.BASE_URL
 
/**
 * 用户登录
 */
export function login(username, password, code, uuid) {
  return http.get(`mock/login.json`, { baseURL })
} 

// 退出方法
export function logout() {
  return http.get(`mock/logout.json`, { baseURL })
}

/**
 * 获取登录用户信息  
 */
export function getUserProfile() {
  return http.get(`mock/profile.json`, { baseURL })
} 
/**
 * 获取菜单  
 */
export function getRouters() {
  return http.get(`mock/menu.json`, { baseURL })
}

/**
 * 查询岗位列表  
 */ 
export function listPost(query) {
  return http.get(`mock/post.json`, { baseURL })
}



/**
 * 获取角色
 * @param {*} data 
 * @returns 
 */
export function getRoles(data) {
  return http.get(`mock/roles.json`, { params: data }, { baseURL })
}

/**
 * 获取部门
 * @param {*} data 
 * @returns 
 */
export function getDepartments(data) {
  return http.get(`mock/departments.json`, { params: data }, { baseURL })
}

/**
 * 获取职员
 * @param {*} data 
 * @returns 
 */
export function getEmployees(data) {
  return http.get(`mock/employees.json`, { params: data }, { baseURL })
}


/**
 * 获取条件字段
 * @param {*} data 
 * @returns 
 */
export function getConditions(data) {
  return http.get(`mock/conditions.json`, { params: data }, { baseURL })
}

/**
 * 获取审批数据
 * @param {*} data 
 * @returns 
 */
export function getWorkFlowData(data) {
  return http.get(`mock/data.json`, { params: data }, { baseURL })
}

 /**
 * 获取用户分页信息
 * @param {*} data 
 * @returns 
 */
 export function getUserPageList(pageDto,qVO) {
  let data = {
    "pageDto": pageDto,
    "taskMgmtVO": qVO
  } 
  return http.post(`/user/getUserPageList`, data)
}
/**
 * 三方接入 获取数据根据url
 * @returns 
 */
export function getDynamicsList(url) {
  return http.get(url)
} 