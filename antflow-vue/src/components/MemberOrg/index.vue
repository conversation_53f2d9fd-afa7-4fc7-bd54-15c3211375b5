<template>
  <div class="member-org-wrap">
    <el-tree-select
      ref="treeRef"
      v-model="pageData.memberOrgIds"
      :data="pageData.treeList"
      node-key="value"
      :multiple="pageData.selectProps.multiple"
      :check-strictly="pageData.selectProps.checkStrictly"
      clearable
      filterable
      remote
      :remote-method="remoteMethod"
      :loading="pageData.optionLoading"
      :load="loadNode"
      :props="{ isLeaf: 'isLeaf' }"
      lazy
      @node-click="handleNodeClick"
      @clear="handleClear"
      @focus="handleFocus"
      placeholder="请选择或搜索"
      :default-expanded-keys="pageData.expandedKeys"
    >
      <template #default="{ node, data }">
        <div class="custom-tree-node" :key="`${node.type}_${node.value}`">
          <span v-if="pageData.showQueryList && data.namePath" class="tree-node-label">{{ `${formattedNamePath(data.namePath, 26, 20)}` }}：</span>
          <span class="tree-node-label">{{ node.label }}</span>
        </div>
      </template>
      <template v-if="pageData.selectProps.multiple" #tag>
        <div class="select-tag">
          <el-tag v-for="item in pageData.memberOrgList" :key="`${item.ehrSource}_${item.id}`" closable type="info" @close="handleRemove(item)" class="ellipsis-tag">
            <span class="tag-text">{{ getShowName(item) }}</span>
          </el-tag>
        </div>
      </template>
      <template v-else #label>
        <div class="select-tag">
          <el-tag v-for="item in pageData.memberOrgList" :key="`${item.ehrSource}_${item.id}`" closable type="info" @close="handleRemove(item)" class="single-tag">
            <span class="tag-text" :title="getShowName(item)">{{ `${formattedNamePath(getShowName(item), 26, 20)}` }}</span>
          </el-tag>
        </div>
      </template>
    </el-tree-select>
  </div>
</template>

<script setup>
import { computed, reactive, ref, nextTick, onMounted } from 'vue'
import { getOrgTree, queryByNameFuzzy } from '@/api/label'
import { EHR_SOURCE } from '@/utils/constants'
import { formattedNamePath, normalizeTreeNodes } from '@/utils/index'

const emits = defineEmits(['change', 'update:dataList'])
const props = defineProps({
  type: { // 1成员 2部门
    type: [String, Number],
    default: '1'
  },
  dataList: {
    type: [String, Array],
    default: () => []
  },
  selectProps: {
    type: Object,
    default: () => ({
      multiple: true,
      checkStrictly: false
    })
  }
})
const propsType = computed(() => {
  return [1, 3].includes(Number(props.type)) ? 1 : (props.type || 1)
})

const treeRef = ref()
const pageData = reactive({
  treeList: [], // 树数据列表
  memberOrgList: [], // 选择的人/组织
  memberOrgIds: '',  // 选择的人/组织 id
  optionLoading: false,
  showQueryList: false, // 是否显示模糊查询列表
  selectProps: {
    multiple: true,
    checkStrictly: false,
    ...(props.selectProps || {})
  },
  expandedKeys: [], // 展开的节点keys
})

// 初始化tree数据
const initTreeList = () => {
  let type = propsType.value
  let allTreeList = sessionStorage.getItem(`allTreeList_${type}`)
  allTreeList = allTreeList ? JSON.parse(allTreeList) : {}

  pageData.treeList = EHR_SOURCE.map(item => {
    item.children = allTreeList[item.ehrSource]?.length ? allTreeList[item.ehrSource] : null
    return item
  })
}
// 设置树选中
const setTreeChecked = () => {
  nextTick(() => {
    if(treeRef.value) {
      if(pageData.selectProps.multiple) {
        pageData.memberOrgIds = pageData.memberOrgList.map(item => item.id)
        treeRef.value.setCheckedKeys(pageData.memberOrgIds)
      } else {
        pageData.memberOrgIds = pageData.memberOrgList[0]?.id || ''
        treeRef.value.setCheckedKeys(pageData.memberOrgIds ? [pageData.memberOrgIds] : [])
      }
    }
  })
}

onMounted(() => {
  initTreeList()
  pageData.memberOrgList = (props.dataList || []).map(item => {
    let id = item.id
    if(item.controlDataType == 2) {
      id = `${item.ehrSource}_${id}`
    }
    return { ...item, id }
  })
  setTreeChecked()
})

// 获取树列表
const getTreeList = async (ehrSource) => {
  let type = propsType.value
  let allTreeList = sessionStorage.getItem(`allTreeList_${type}`)
  allTreeList = allTreeList ? JSON.parse(allTreeList) : {}
  if(allTreeList[ehrSource]?.length) {
    return allTreeList[ehrSource]
  }

  let params = {
    ehrSource,
    type
  }
  let res = await getOrgTree(params)
  allTreeList[ehrSource] = normalizeTreeNodes(res?.data?.children || [])
  sessionStorage.setItem(`allTreeList_${type}`, JSON.stringify(allTreeList))
  return allTreeList[ehrSource]
}

// 懒加载
const loadNode = async (node, resolve) => {
  console.log('loadNode: ', node, node.childNodes)
  
  // 根节点，返回数据源列表
  if (!node || node.level === 0) {
    return resolve(pageData.treeList || EHR_SOURCE || [])
  }
  
  // 第一层节点，需要加载对应数据源的树
  if (node.level === 1) {
    try {
      let res = await getTreeList(node.data.ehrSource)
      const processedData = res.map(item => ({
        ...item,
        isLeaf: !item.children || item.children.length === 0,
      }));
      return resolve(processedData)
    } catch (error) {
      console.error('Failed to load tree data:', error)
      return resolve([])
    }
  }

  // 叶子节点直接返回空数组
  if (node.isLeaf) {
    return resolve([])
  }

  // 其他层级节点，处理 children 数据
  (node.data.children || []).forEach(child => {
    child.isLeaf = !child.children || child.children.length === 0
  })
  
  console.log('node.children: ', node.data.children || [])
  return resolve(node.data.children || [])
}

// 点击树节点
const handleNodeClick = (node, nodeProp) => {
  console.log('handleNodeClick: ', node, nodeProp)
  if([1, 2].includes(Number(props.type)) && node.type != props.type) {
    nodeProp.checked = false
    return
  }
  
  let empId = node.value
  let empIds = pageData.memberOrgList.map(item => item.id)
  const isChecked = nodeProp.checked = !nodeProp.checked;
  const isExisting = empIds.includes(empId);
  let ehrSource = ''
  let namePath = nodeProp.data.namePath || ''
  if(nodeProp.level === 1){
    ehrSource = nodeProp.data.ehrSource
    namePath = nodeProp.data.namePath || nodeProp.data.label
  } else {
    let parent = nodeProp.parent
    while(parent && parent.level !== 1){
      if(!nodeProp.data.namePath) {
        namePath = namePath ? parent?.data?.label + '/' + namePath : parent?.data?.label
      }
      parent = parent.parent;
    }
    ehrSource = parent?.data?.ehrSource || '';
    if(!nodeProp.data.namePath) {
      namePath = namePath ? parent.data.label + '/' + namePath : parent.data.label
    }
  }
  if(isChecked && !isExisting) { // 选中 但不存在
    let currentItem = {
      ehrSource,
      namePath,
      controlDataType: node.type,
      id: empId,
      name: node.label
    }
    if(pageData.selectProps.multiple) {
      pageData.memberOrgList.push(currentItem)
    } else {
      pageData.memberOrgList = [currentItem]
    }
  } else if(!isChecked && isExisting) { // 取消选中 但存在
    pageData.memberOrgList = pageData.memberOrgList.filter(item => (item.ehrSource != ehrSource) || (item.ehrSource == ehrSource && item.id != empId))
  }
  setTreeChecked()
  emitOuter()
}
// 模糊查询
const fetchQueryData = (query) => {
  let params = {
    name: query.trim(),
    queryType: props.type
  }
  pageData.optionLoading = true
  queryByNameFuzzy(params).then(response => {
    let users = response?.data?.users || []
    let orgs = response?.data?.orgs || []
    let list = [ ...users, ...orgs ]
    pageData.treeList = list.map(item => ({
      ...item,
      label: item.name,
      ehrSource: item.ehrSource,
      value: item.id,
      isLeaf: true
    }))
    // treeList更换时重新设置选中
    setTreeChecked()
  }).finally(() => {
    pageData.showQueryList = true
    pageData.optionLoading = false
  })
}
// 远程获取搜索节点数据
const remoteMethod = (query) => {
  if (query) {
    fetchQueryData(query)
  } else {
    pageData.showQueryList = false

    initTreeList()
    // treeList更换时重新设置选中
    setTreeChecked()
  }
}

// 删除标签
const handleRemove = (item) => {
  // 移除选中 返回ehrSource不同 或者 ehrSource相同但id不同
  pageData.memberOrgList = pageData.memberOrgList.filter(el => (item.ehrSource != el.ehrSource) || (item.ehrSource == el.ehrSource && item.id != el.id))
  setTreeChecked()
  emitOuter()
}
// 清空
const handleClear = () => {
  pageData.memberOrgList = []
  setTreeChecked()
  emitOuter()
}

// 获取焦点
const handleFocus = () => {
  pageData.expandedKeys = pageData.memberOrgIds && typeof(pageData.memberOrgIds) === 'string' ? [pageData.memberOrgIds] : pageData.memberOrgIds || []
  console.log('pageData.expandedKeys: ', pageData.expandedKeys)
}

// 获取展示name
const getShowName = (item) => {
  return item.namePath ? `${item.namePath}/${item.name}` : item.name
}

// 向外发送数据
const emitOuter = () => {
  let outerList = pageData.memberOrgList.map(item => {
    let id = item.id
    if(item.controlDataType == 2) {
      id = id.split('_')[1]
    }
    return {
      ...item,
      id
    }
  })
  console.log('emitOuter: ', pageData.memberOrgList, outerList)
  emits('change', outerList)
  emits('update:dataList', outerList);
}

defineExpose({
  clearMemberOrg: handleClear
})
</script>

<style lang="scss" scoped>
.member-org-wrap {
  width: 100%;
  .select-tag {
    max-width: 100%;
    box-sizing: border-box;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    .ellipsis-tag {
      height: auto;
      max-width: 100%;
      box-sizing: border-box;
      line-height: 1.5;
      padding: 3px 5px;
      .tag-text {
        white-space: break-spaces;
      }
    }
    .single-tag {
      max-width: 100%;
    }
  }
}
</style>
