<template>
    <div class="form-container">
        <el-form ref="ruleFormRef" :model="form" :rules="rules" label-width="auto"
            style="max-width: 600px;margin: auto;">

            <el-form-item label="模版名称" prop="bpmnName"> 
                <el-input v-model="form.bpmnName" placeholder="请输入模版名称" :maxlength="20" show-word-limit :style="{ width: '100%' }" />
            </el-form-item>

            <el-form-item label="模版分组" prop="templateGroupId">
                <el-select filterable v-model="form.templateGroupId" placeholder="请选择模版分组" :style="{ width: '100%' }">
                    <el-option v-for="(item, index) in formCodeOptions" :key="index" :label="item.name" :value="item.id" />
                </el-select>
            </el-form-item>

            <el-form-item label="模版管理员" prop="adminInfo">
                <MemberOrg type="1" v-model:dataList="form.adminInfo" />
            </el-form-item>

            <el-form-item label="审批人去重" prop="deduplicationType">
                <el-select v-model="form.deduplicationType" placeholder="请选择去重类型" :style="{ width: '100%' }">
                    <el-option v-for="(item, index) in duplicateOptions" :key="index" :label="item.label"
                        :value="item.value"></el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="模版说明" prop="remark">
                <el-input v-model="form.remark" type="textarea" placeholder="请输入模版说明" :maxlength="100" show-word-limit
                    :autosize="{ minRows: 4, maxRows: 4 }" :style="{ width: '100%' }"></el-input>
            </el-form-item>

        </el-form>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue'
import { NodeUtils } from '@/utils/flow/nodeUtils'
import { getTemplateGroupList } from "@/api/template"
import MemberOrg from "@/components/MemberOrg/index.vue"
const { query } = useRoute(); 
const { proxy } = getCurrentInstance()
const emit = defineEmits(['nextChange'])
let loading = ref(false);
let props = defineProps({
    flowType: {
        type: String,
        default: () => (''),
    },
    basicData: {
        type: Object,
        default: () => (null),
    }
});

const generatorID = "PROJECT_" + NodeUtils.idGenerator();
const ruleFormRef = ref(null);
let duplicateOptions = [
    { "label": "不去重", "value": 1 },
    // { "label": "前去重", "value": 2 },
    { "label": "后去重", "value": 3 }
];

let formCodeOptions = ref([]);
const form = reactive({
    bpmnName: '',
    bpmnCode: generatorID,
    bpmnType: 1,
    templateGroupId: undefined,
    adminInfo: [], // 添加管理员信息字段
    remark: '',
    effectiveStatus: false,
    deduplicationType: 1 // 默认"不去重"
})

onMounted(async () => { 
    if (!proxy.isObjEmpty(props.basicData) && !proxy.isObjEmpty(props.basicData.templateGroupId)) {
        form.bpmnName = props.basicData.bpmnName;
        form.bpmnCode = props.basicData.bpmnCode;
        form.templateGroupId = props.basicData.templateGroupId;
        form.adminInfo = props.basicData.adminInfo || []; // 初始化管理员信息
        form.remark = props.basicData.remark;
        form.deduplicationType = props.basicData.deduplicationType;
    }
    else {
        form.bpmnCode = generatorID; 
        form.templateGroupId = query.fc;
        form.bpmnName = decodeURIComponent(query.fcname??''); 
    }
    getFromCodeList()
});

/**获取模版扽组列表 */
const getFromCodeList = async () => {
    let params = {
      pageDto: {
        page: 1,
        pageSize: 1000
      }
    }
   loading.value = true;
   await getTemplateGroupList(params).then((res) => {
        if (res.code == 200) {
            formCodeOptions.value = res?.data || []
        }
    }).catch(err => {
        proxy.$modal.msgError("模版分组加载列表失败:" + err.message);
    }).finally(() => {
        loading.value = false;
    })
}

let rules = {
    bpmnName: [{
        required: true,
        message: '请输入模版名称',
        trigger: 'blur'
    }],
    templateGroupId: [{
        required: true,
        message: '请选择模版分组',
        trigger: 'change'
    }]
};

// 给父级页面提供得获取本页数据得方法
const getData = () => {
    return new Promise((resolve, reject) => {
        proxy.$refs['ruleFormRef'].validate((valid, fields) => {
            if (!valid) {
                emit('nextChange', { label: "基础设置", key: "basicSetting" })
                reject({ valid: false });
            }
            form.effectiveStatus = form.effectiveStatus ? 1 : 0;
            // 添加管理员信息到返回数据中
            const formData = {
                ...form,
                adminInfo: form.adminInfo
            };
            resolve({ formData })
        })
    })
};
defineExpose({
    getData
})
</script>
<style scoped>
.form-container {
    background: white !important;
    padding: 30px;
    max-width: 750px;
    min-height: 70vh;
    left: 0;
    bottom: 0;
    right: 0;
    margin: auto;
}
</style>