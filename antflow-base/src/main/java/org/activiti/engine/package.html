<html>
  <body>
    Public API of the Activiti engine.<br/><br/>
    Typical usage of the API starts by the creation of a {@link org.activiti.engine.ProcessEngineConfiguration}
    (typically based on a configuration file), from which a {@link org.activiti.engine.ProcessEngine} can be obtained.<br/><br/>
    Through the services obtained from such a {@link org.activiti.engine.ProcessEngine}, BPM and workflow operation 
    can be executed:<br/><br/>
    
    <b>{@link org.activiti.engine.RepositoryService}: </b> Manages {@link org.activiti.engine.repository.Deployment}s <br/>

    <b>{@link org.activiti.engine.RuntimeService}: </b> For starting and searching {@link org.activiti.engine.runtime.ProcessInstance}s <br/>
    
    <b>{@link org.activiti.engine.TaskService}: </b> Exposes operations to manage human (standalone) {@link org.activiti.engine.task.Task}s, 
    such as claiming, completing and assigning tasks<br/>

    <b>{@link org.activiti.engine.IdentityService}: </b> Used for managing {@link org.activiti.engine.identity.User}s, 
    {@link org.activiti.engine.identity.Group}s and the relations between them<br/>
        
    <b>{@link org.activiti.engine.ManagementService}: </b> Exposes engine admin and maintenance operations,
    which have no relation to the runtime exection of business processes<br/>
    
    <b>{@link org.activiti.engine.HistoryService}: </b> Exposes information about ongoing and past process instances.<br/>
    
    <b>{@link org.activiti.engine.FormService}: </b> Access to form data and rendered forms for starting new process instances and completing tasks.<br/>
  </body>
</html> 