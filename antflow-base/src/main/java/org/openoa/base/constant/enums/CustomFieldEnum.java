package org.openoa.base.constant.enums;

import lombok.Getter;

@Getter
public enum CustomFieldEnum {
    MEMBER(1,"member", "成员控件"),
    ORG(2, "org","组织控件"),
    TAG(3,"tag", "标签控件"),
	// 自定义部门条件,上面的ORG是指成员控件在选条件的时候选了一个组织，而ORGANIZATION是指部门控件在条件选择的时候选择的组织
	ORGANIZATION(4,"organization", "部门控件"),
    UNKNOWN(100,"unknown", "未知"),
    ;
    private Integer no;
    private String code;
    private String desc;

    CustomFieldEnum(Integer no , String code, String desc) {
        this.no = no;
        this.code = code;
        this.desc = desc;
    }

    public static boolean isCustomerField(String code) {
        for (CustomFieldEnum value : CustomFieldEnum.values()) {
            if (value.getCode().equals(code)) {
                return true;
            }
        }
        return false;
    }

    public static CustomFieldEnum getByCode(String code) {
        for (CustomFieldEnum value : CustomFieldEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return UNKNOWN;
    }
    public static CustomFieldEnum getByNo(Integer no) {
        for (CustomFieldEnum value : CustomFieldEnum.values()) {
            if (value.getNo().equals(no)) {
                return value;
            }
        }
        return UNKNOWN;
    }
}
