package org.openoa.base.service.wecom;

public enum NoticeMsgEnum {
    PROCESS_STATE_CHANGE(0, "审批发生流转"),
    PROCESS_APPROVAL(1, "审批流程"),
    PROCESS_COPY(2, "审批抄送"),
    PROCESS_REMINDER(3, "流程催办");
    private Integer code;
    private String msg;

    NoticeMsgEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static String getMsgByCode(Integer code) {
        for (NoticeMsgEnum value : values()) {
            if (value.code.equals(code)) {
                return value.msg;
            }
        }
        return null;
    }
}
