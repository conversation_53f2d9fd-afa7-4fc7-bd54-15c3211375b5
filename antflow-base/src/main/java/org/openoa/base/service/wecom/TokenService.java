package org.openoa.base.service.wecom;

import lombok.extern.slf4j.Slf4j;
import org.openoa.base.constant.ServiceConstant;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class TokenService {


    @Resource
    private StringRedisTemplate stringRedisTemplate;

    /***
     * 缓存企业访问accessToken
     * @param corpId
     * @param accessToken
     * <AUTHOR>
     * @date 2025/4/25 17:05
     * @version 1.0.0
     **/
    public void saveCorpAccessToken(String corpIdAndagentSecret, String accessToken) {
        log.info("corpId:{},accessToken:{}", corpIdAndagentSecret, accessToken);
        try {
			// https://developer.work.weixin.qq.com/devtool/query?e=40029 企微的oauth_code 有效期为5分钟
            saveToken(ServiceConstant.Wecom.getWecomCorpTokenCacheKey(corpIdAndagentSecret), accessToken, 5, TimeUnit.MINUTES);
        } catch (Exception e) {
            log.error("保存token失败. corpId: {}, accessToken={} ,error: {}", corpIdAndagentSecret,accessToken, e.getMessage());
        }
    }

    /***
     * 缓存登录用户信息
     * @param source
     * @param userId
     * @param userData
     * <AUTHOR>
     * @date 2025/4/25 17:57
     * @version 1.0.0
     * @return void
    **/
    public void saveLoginUserData(Integer source,String userId, String userData) {
        log.info("source={} userId:{},userData:{}",source, userId, userData);
        try {
            saveToken(ServiceConstant.Wecom.getLoginUserCacheKey(source,userId), userData, 5, TimeUnit.MINUTES);
        } catch (Exception e) {
            log.error("保存token失败. source={} userId:{},userData:{}",source, userId,userData, e);
        }
    }

    /***
     * 获取缓存的登录用户信息
     * @param source
     * @param userId
     * <AUTHOR>
     * @date 2025/4/25 18:13
     * @version 1.0.0
     * @return java.util.Optional<java.lang.String>
    **/
    public Optional<String> getLoginUserData(Integer source, String userId) {
        log.info("source={} userId:{}",source, userId);
        try {
            return Optional.ofNullable(get(ServiceConstant.Wecom.getLoginUserCacheKey(source, userId)));
        } catch (Exception e) {
            log.error("保存token失败. source={} userId:{}",source, userId, e);
            return Optional.empty();
        }
    }

    /***
     * 获取企业访问accessToken
     * @param corpId
     * <AUTHOR>
     * @date 2025/4/25 17:05
     * @version 1.0.0
     * @return java.lang.String
    **/
    public String getCorpAccessToken(String corpIdAndagentSecret) {
        log.info("从缓存获取企业访问accessToken corpId:{}", corpIdAndagentSecret);
        return get(ServiceConstant.Wecom.getWecomCorpTokenCacheKey(corpIdAndagentSecret));
    }

    /**
     * 保存token
     */
    public boolean saveToken(String key, String token, long timeout, TimeUnit unit) {
        try {
            stringRedisTemplate.opsForValue().set(key, token, timeout, unit);
            return true;
        } catch (Exception e) {
            log.error("保存token失败. key: {}, error: {}", key, e.getMessage());
            return false;
        }
    }
    
    /**
     * 获取
     */
    public String get(String key) {
        try {
            return stringRedisTemplate.opsForValue().get(key);
        } catch (Exception e) {
            log.error("获取token失败. key: {}, error: {}", key, e.getMessage());
            return null;
        }
    }
    
    /**
     * 删除token
     */
    public boolean delete(String key) {
        try {
            return Boolean.TRUE.equals(stringRedisTemplate.delete(key));
        } catch (Exception e) {
            log.error("删除token失败. key: {}, error: {}", key, e.getMessage());
            return false;
        }
    }
    
    /**
     * 设置token过期时间
     */
    public boolean expireToken(String key, long timeout, TimeUnit unit) {
        try {
            return Boolean.TRUE.equals(stringRedisTemplate.expire(key, timeout, unit));
        } catch (Exception e) {
            log.error("设置token过期时间失败. key: {}, error: {}", key, e.getMessage());
            return false;
        }
    }
    
    /**
     * 判断token是否存在
     */
    public boolean hasToken(String key) {
        try {
            return Boolean.TRUE.equals(stringRedisTemplate.hasKey(key));
        } catch (Exception e) {
            log.error("判断token是否存在失败. key: {}, error: {}", key, e.getMessage());
            return false;
        }
    }
}