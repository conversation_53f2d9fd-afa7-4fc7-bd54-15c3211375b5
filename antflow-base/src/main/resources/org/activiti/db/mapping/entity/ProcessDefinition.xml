<?xml version="1.0" encoding="UTF-8" ?> 

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd"> 
  
<mapper namespace="org.activiti.engine.impl.persistence.entity.ProcessDefinitionEntity">
  
  <!-- PROCESSDEFINITION INSERT -->

  <insert id="insertProcessDefinition" parameterType="org.activiti.engine.impl.persistence.entity.ProcessDefinitionEntity">
    insert into ${prefix}ACT_RE_PROCDEF(ID_, REV_, CATEGORY_, NAME_, KEY_, VERSION_, DEPLOYMENT_ID_, RESOURCE_NAME_, DGRM_RESOURCE_NAME_, DESCRIPTION_, HAS_START_FORM_KEY_, HAS_GRAPHICAL_NOTATION_ , SUSPENSION_STATE_, TENANT_ID_)
    values (#{id, jdbcType=VARCHAR},
    		1,
            #{category, jdbcType=VARCHAR},
            #{name, jdbcType=VARCHAR},
            #{key, jdbcType=VARCHAR}, 
            #{version, jdbcType=INTEGER},
            #{deploymentId, jdbcType=VARCHAR},
            #{resourceName, jdbcType=VARCHAR},
            #{diagramResourceName, jdbcType=VARCHAR},
            #{description, jdbcType=VARCHAR},
            #{hasStartFormKey, jdbcType=BOOLEAN},
            #{isGraphicalNotationDefined, jdbcType=BOOLEAN},
            #{suspensionState, jdbcType=INTEGER},
            #{tenantId, jdbcType=VARCHAR})
  </insert>

  <insert id="bulkInsertProcessDefinition" parameterType="java.util.List">
    INSERT INTO ${prefix}ACT_RE_PROCDEF(ID_, REV_, CATEGORY_, NAME_, KEY_, VERSION_, DEPLOYMENT_ID_, RESOURCE_NAME_, DGRM_RESOURCE_NAME_, DESCRIPTION_, HAS_START_FORM_KEY_, HAS_GRAPHICAL_NOTATION_ , SUSPENSION_STATE_, TENANT_ID_)
    VALUES 
      <foreach collection="list" item="processDefinition" index="index" separator=","> 
        (#{processDefinition.id, jdbcType=VARCHAR},
         1,
         #{processDefinition.category, jdbcType=VARCHAR},
         #{processDefinition.name, jdbcType=VARCHAR},
         #{processDefinition.key, jdbcType=VARCHAR}, 
         #{processDefinition.version, jdbcType=INTEGER},
         #{processDefinition.deploymentId, jdbcType=VARCHAR},
         #{processDefinition.resourceName, jdbcType=VARCHAR},
         #{processDefinition.diagramResourceName, jdbcType=VARCHAR},
         #{processDefinition.description, jdbcType=VARCHAR},
         #{processDefinition.hasStartFormKey, jdbcType=BOOLEAN},
         #{processDefinition.isGraphicalNotationDefined, jdbcType=BOOLEAN},
         #{processDefinition.suspensionState, jdbcType=INTEGER},
         #{processDefinition.tenantId, jdbcType=VARCHAR})
    </foreach>
  </insert>

  <insert id="bulkInsertProcessDefinition_oracle" parameterType="java.util.List">
    INSERT ALL 
    <foreach collection="list" item="processDefinition" index="index"> 
      INTO ${prefix}ACT_RE_PROCDEF(ID_, REV_, CATEGORY_, NAME_, KEY_, VERSION_, DEPLOYMENT_ID_, RESOURCE_NAME_,
      DGRM_RESOURCE_NAME_, DESCRIPTION_, HAS_START_FORM_KEY_, HAS_GRAPHICAL_NOTATION_ , SUSPENSION_STATE_, TENANT_ID_) VALUES 
        (#{processDefinition.id, jdbcType=VARCHAR},
         1,
         #{processDefinition.category, jdbcType=VARCHAR},
         #{processDefinition.name, jdbcType=VARCHAR},
         #{processDefinition.key, jdbcType=VARCHAR}, 
         #{processDefinition.version, jdbcType=INTEGER},
         #{processDefinition.deploymentId, jdbcType=VARCHAR},
         #{processDefinition.resourceName, jdbcType=VARCHAR},
         #{processDefinition.diagramResourceName, jdbcType=VARCHAR},
         #{processDefinition.description, jdbcType=VARCHAR},
         #{processDefinition.hasStartFormKey, jdbcType=BOOLEAN},
         #{processDefinition.isGraphicalNotationDefined, jdbcType=BOOLEAN},
         #{processDefinition.suspensionState, jdbcType=INTEGER},
         #{processDefinition.tenantId, jdbcType=VARCHAR})
    </foreach>
    SELECT * FROM dual
  </insert>
  
  <!-- PROCESSDEFINITION UPDATE -->

  <update id="updateProcessDefinition" parameterType="org.activiti.engine.impl.persistence.entity.ProcessDefinitionEntity">
    update ${prefix}ACT_RE_PROCDEF set
      REV_ = #{revisionNext, jdbcType=INTEGER},    
      SUSPENSION_STATE_ = #{suspensionState, jdbcType=INTEGER},
      CATEGORY_ = #{category, jdbcType=VARCHAR}
    where ID_ = #{id, jdbcType=VARCHAR}
      and REV_ = #{revision, jdbcType=INTEGER}
  </update>
  
  <update id="updateProcessDefinitionTenantIdForDeploymentId" parameterType="java.util.Map">
    update ${prefix}ACT_RE_PROCDEF set
      TENANT_ID_ = #{tenantId, jdbcType=VARCHAR}
    where
      DEPLOYMENT_ID_ = #{deploymentId, jdbcType=VARCHAR}
  </update>

  <!-- PROCESSDEFINITION DELETE -->

  <delete id="deleteProcessDefinitionsByDeploymentId" parameterType="string">
    delete from ${prefix}ACT_RE_PROCDEF where DEPLOYMENT_ID_ = #{deploymenId} 
  </delete>
  
  <!-- PROCESSDEFINITION RESULTMAP -->

  <resultMap id="processDefinitionResultMap" type="org.activiti.engine.impl.persistence.entity.ProcessDefinitionEntity">
    <id property="id" column="ID_" jdbcType="VARCHAR" />
    <result property="revision" column="REV_" />
    <result property="category" column="CATEGORY_" />
    <result property="name" column="NAME_" />
    <result property="key" column="KEY_" jdbcType="VARCHAR" />
    <result property="version" column="VERSION_" jdbcType="INTEGER"/>
    <result property="deploymentId" column="DEPLOYMENT_ID_" jdbcType="VARCHAR"/>
    <result property="resourceName" column="RESOURCE_NAME_" jdbcType="VARCHAR"/>
    <result property="tenantId" column="TENANT_ID_" jdbcType="VARCHAR" />
    <result property="diagramResourceName" column="DGRM_RESOURCE_NAME_" jdbcType="VARCHAR"/>
    <result property="description" column="DESCRIPTION_" jdbcType="VARCHAR" />
    <result property="hasStartFormKey" column="HAS_START_FORM_KEY_" jdbcType="BOOLEAN"/>
    <result property="isGraphicalNotationDefined" column="HAS_GRAPHICAL_NOTATION_" jdbcType="BOOLEAN" />
    <result property="suspensionState" column="SUSPENSION_STATE_" jdbcType="INTEGER"/>
  </resultMap>

  <!-- PROCESSDEFINITION SELECT -->

  <select id="selectProcessDefinition" parameterType="string" resultMap="processDefinitionResultMap">
    select * from ${prefix}ACT_RE_PROCDEF where ID_ = #{processDefinitionId}
  </select>

  <select id="selectProcessDefinitionById" parameterType="string" resultMap="processDefinitionResultMap">
    select * from ${prefix}ACT_RE_PROCDEF where ID_ = #{processDefinitionId}
  </select>

  <select id="selectProcessDefinitionsByQueryCriteria" parameterType="org.activiti.engine.impl.ProcessDefinitionQueryImpl" resultMap="processDefinitionResultMap">
  	${limitBefore}
    select RES.* ${limitBetween}
    <include refid="selectProcessDefinitionsByQueryCriteriaSql"/>
    ${orderBy}
    ${limitAfter}
  </select>

  <select id="selectProcessDefinitionCountByQueryCriteria" parameterType="org.activiti.engine.impl.ProcessDefinitionQueryImpl" resultType="long">
    select count(RES.ID_)
    <include refid="selectProcessDefinitionsByQueryCriteriaSql"/>
  </select>
  
  <sql id="selectProcessDefinitionsByQueryCriteriaSql">  
    from ${prefix}ACT_RE_PROCDEF RES
    <if test="eventSubscriptionType != null">
    	inner join ${prefix}ACT_RU_EVENT_SUBSCR EVT on RES.ID_ = EVT.CONFIGURATION_
    </if>
    <where>
      <if test="id != null">
        RES.ID_ = #{id}
      </if>
      <if test="ids != null and ids">
        and RES.ID_ in
        <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="category != null">
        and RES.CATEGORY_ = #{category}
      </if>
      <if test="categoryLike != null">
        and RES.CATEGORY_ like #{categoryLike}${wildcardEscapeClause}
      </if>
      <if test="categoryNotEquals != null">
        and ( RES.CATEGORY_ &lt;&gt; #{categoryNotEquals} OR RES.CATEGORY_ is null )
      </if>
      <if test="name != null">
        and RES.NAME_ = #{name}
      </if>
      <if test="nameLike != null">
        and RES.NAME_ like #{nameLike}${wildcardEscapeClause}
      </if>
      <if test="key != null">
        and RES.KEY_ = #{key}
      </if>
      <if test="keyLike != null">
        and RES.KEY_ like #{keyLike}${wildcardEscapeClause}
      </if>
      <if test="resourceName != null">
        and RES.RESOURCE_NAME_ = #{resourceName}
      </if>
      <if test="resourceNameLike != null">
        and RES.RESOURCE_NAME_ like #{resourceNameLike}${wildcardEscapeClause}
      </if>
      <if test="version != null">
        and RES.VERSION_ = #{version}
      </if>
      <if test="versionGt != null">
        and RES.VERSION_ &gt; #{versionGt}
      </if>
      <if test="versionGte != null">
        and RES.VERSION_ &gt;= #{versionGte}
      </if>
      <if test="versionLt != null">
        and RES.VERSION_ &lt; #{versionLt}
      </if>
      <if test="versionLte != null">
        and RES.VERSION_ &lt;= #{versionLte}
      </if>
      <if test="deploymentId != null">
        and RES.DEPLOYMENT_ID_ = #{deploymentId}
      </if>
      <if test="deploymentIds != null and !deploymentIds.isEmpty()">
        and RES.DEPLOYMENT_ID_ in
        <foreach item="item" index="index" collection="deploymentIds" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="latest">
        and RES.VERSION_ = (select max(VERSION_) from ${prefix}ACT_RE_PROCDEF where KEY_ = RES.KEY_ 
             <if test="tenantId != null">
	           and TENANT_ID_ = #{tenantId}
	         </if>
	         <if test="tenantIdLike != null">
	           and TENANT_ID_ like #{tenantIdLike}${wildcardEscapeClause}
	         </if>
	         <if test="withoutTenantId">
	           and (TENANT_ID_ = '' or TENANT_ID_ is null)
	         </if>
	         <if test="tenantId == null and tenantIdLike == null and !withoutTenantId">
	           and ( (TENANT_ID_ IS NOT NULL and TENANT_ID_ = RES.TENANT_ID_) or (TENANT_ID_ IS NULL and RES.TENANT_ID_ IS NULL) )
	         </if>
        )
      </if>
      <if test="suspensionState != null">
        and (RES.SUSPENSION_STATE_ = #{suspensionState.stateCode})
      </if>
       <if test="tenantId != null">
        and RES.TENANT_ID_ = #{tenantId}
      </if>
      <if test="tenantIdLike != null">
        and RES.TENANT_ID_ like #{tenantIdLike}${wildcardEscapeClause}
      </if>
      <if test="withoutTenantId">
        and (RES.TENANT_ID_ = '' or RES.TENANT_ID_ is null)
      </if>
      <if test="eventSubscriptionType != null">
      	and (EVT.EVENT_TYPE_ = #{eventSubscriptionType} and EVT.EVENT_NAME_ = #{eventSubscriptionName}) 
      </if>
      <if test="authorizationUserId != null">
        AND (exists (select ID_  from ${prefix}ACT_RU_IDENTITYLINK  IDN where IDN.PROC_DEF_ID_ = RES.ID_ and IDN.USER_ID_ = #{authorizationUserId})
        <if test="authorizationGroups != null &amp;&amp; authorizationGroups.size() &gt; 0">
         OR exists (select ID_ from ${prefix}ACT_RU_IDENTITYLINK  IDN where IDN.PROC_DEF_ID_ = RES.ID_ and IDN.GROUP_ID_ IN
            <foreach item="group" index="index" collection="authorizationGroups" 
                     open="(" separator="," close=")">
              #{group}
            </foreach>
         )
         </if>
        )
      </if>
    </where>
  </sql>
    
  <select id="selectProcessDefinitionByDeploymentAndKey" parameterType="map" resultMap="processDefinitionResultMap">
    select * 
    from ${prefix}ACT_RE_PROCDEF 
    where DEPLOYMENT_ID_ = #{deploymentId}
      and KEY_ = #{processDefinitionKey}
      and (TENANT_ID_ = '' or TENANT_ID_ is null)
  </select>
  
   <select id="selectProcessDefinitionByDeploymentAndKeyAndTenantId" parameterType="map" resultMap="processDefinitionResultMap">
    select * 
    from ${prefix}ACT_RE_PROCDEF 
    where DEPLOYMENT_ID_ = #{deploymentId}
      and KEY_ = #{processDefinitionKey}
      and TENANT_ID_ = #{tenantId}
  </select>
    
  <select id="selectLatestProcessDefinitionByKey" parameterType="string" resultMap="processDefinitionResultMap">
    select *
    from ${prefix}ACT_RE_PROCDEF 
    where KEY_ = #{key} and
          (TENANT_ID_ = ''  or TENANT_ID_ is null) and
          VERSION_ = (select max(VERSION_) from ${prefix}ACT_RE_PROCDEF where KEY_ = #{processDefinitionKey} and (TENANT_ID_ = '' or TENANT_ID_ is null))
  </select>
  
  <select id="selectLatestProcessDefinitionByKeyAndTenantId" parameterType="map" resultMap="processDefinitionResultMap">
    select *
    from ${prefix}ACT_RE_PROCDEF 
    where KEY_ = #{processDefinitionKey} and
          TENANT_ID_ = #{tenantId} and
          VERSION_ = (select max(VERSION_) from ${prefix}ACT_RE_PROCDEF where KEY_ = #{processDefinitionKey} and TENANT_ID_ = #{tenantId})
  </select>
  
  <!-- mysql specific sql -->
  <select id="selectProcessDefinitionsByQueryCriteria_mysql" parameterType="org.activiti.engine.impl.ProcessDefinitionQueryImpl" resultMap="processDefinitionResultMap">
  	${limitBefore}
    select distinct RES.* ${limitBetween}
    <include refid="selectProcessDefinitionsByQueryCriteriaSql"/>
    ${orderBy}
    ${limitAfter}
  </select>

  <!-- mysql specific sql -->
  <select id="selectProcessDefinitionCountByQueryCriteria_mysql" parameterType="org.activiti.engine.impl.ProcessDefinitionQueryImpl" resultType="long">
    select distinct count(RES.ID_)
    <include refid="selectProcessDefinitionsByQueryCriteriaSql"/>
  </select>

  <select id="selectProcessDefinitionByNativeQuery" parameterType="java.util.Map" resultMap="processDefinitionResultMap">
    <if test="resultType == 'LIST_PAGE'">
      ${limitBefore}
    </if>
    ${sql}
    <if test="resultType == 'LIST_PAGE'">
      ${limitAfter}
    </if>
  </select>

  <select id="selectProcessDefinitionByNativeQuery_mssql_or_db2" parameterType="java.util.Map" resultMap="processDefinitionResultMap">
    <if test="resultType == 'LIST_PAGE'">
      ${limitBeforeNativeQuery}
    </if>
    ${sql}
    <if test="resultType == 'LIST_PAGE'">
      ${limitAfter}
    </if>
  </select>

  <select id="selectProcessDefinitionCountByNativeQuery" parameterType="java.util.Map" resultType="long">
    ${sql}
  </select>
</mapper>