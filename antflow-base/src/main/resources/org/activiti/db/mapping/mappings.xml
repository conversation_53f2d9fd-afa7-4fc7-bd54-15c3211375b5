<?xml version="1.0" encoding="UTF-8"?>

<!DOCTYPE configuration PUBLIC "-//mybatis.org//DTD Config 3.0//EN" "http://mybatis.org/dtd/mybatis-3-config.dtd">

<configuration>
  <settings>
    <setting name="lazyLoadingEnabled" value="false" />
  </settings>
  <typeAliases>
    <typeAlias type="org.activiti.engine.impl.persistence.ByteArrayRefTypeHandler" alias="ByteArrayRefTypeHandler"/>
  </typeAliases>
  <typeHandlers>
    <typeHandler handler="ByteArrayRefTypeHandler" 
                 javaType="org.activiti.engine.impl.persistence.entity.ByteArrayRef"
                 jdbcType="VARCHAR"/>
  </typeHandlers>
  <mappers>
    <mapper resource="org/activiti/db/mapping/entity/Attachment.xml" />
    <mapper resource="org/activiti/db/mapping/entity/ByteArray.xml" />
    <mapper resource="org/activiti/db/mapping/entity/Comment.xml" />
    <mapper resource="org/activiti/db/mapping/entity/Deployment.xml" />
    <mapper resource="org/activiti/db/mapping/entity/Execution.xml" />
    <mapper resource="org/activiti/db/mapping/entity/Group.xml" />
    <mapper resource="org/activiti/db/mapping/entity/HistoricActivityInstance.xml" />
    <mapper resource="org/activiti/db/mapping/entity/HistoricDetail.xml" />
    <mapper resource="org/activiti/db/mapping/entity/HistoricProcessInstance.xml" />
    <mapper resource="org/activiti/db/mapping/entity/HistoricVariableInstance.xml" />
    <mapper resource="org/activiti/db/mapping/entity/HistoricTaskInstance.xml" />
    <mapper resource="org/activiti/db/mapping/entity/HistoricIdentityLink.xml" />
    <mapper resource="org/activiti/db/mapping/entity/IdentityInfo.xml" />
    <mapper resource="org/activiti/db/mapping/entity/IdentityLink.xml" />
    <mapper resource="org/activiti/db/mapping/entity/Job.xml" />
    <mapper resource="org/activiti/db/mapping/entity/Membership.xml" />
    <mapper resource="org/activiti/db/mapping/entity/Model.xml" />
    <mapper resource="org/activiti/db/mapping/entity/ProcessDefinition.xml" />
    <mapper resource="org/activiti/db/mapping/entity/ProcessDefinitionInfo.xml" />
    <mapper resource="org/activiti/db/mapping/entity/Property.xml" />
    <mapper resource="org/activiti/db/mapping/entity/Resource.xml" />
    <mapper resource="org/activiti/db/mapping/entity/TableData.xml" />
    <mapper resource="org/activiti/db/mapping/entity/Task.xml" />
    <mapper resource="org/activiti/db/mapping/entity/User.xml" />
    <mapper resource="org/activiti/db/mapping/entity/VariableInstance.xml" />
    <mapper resource="org/activiti/db/mapping/entity/EventSubscription.xml" />
    <mapper resource="org/activiti/db/mapping/entity/EventLogEntry.xml" />
  </mappers>
</configuration>
