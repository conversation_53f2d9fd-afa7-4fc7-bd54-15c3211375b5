<?xml version="1.0" encoding="UTF-8" ?> 

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd"> 
  
<mapper namespace="org.activiti.engine.impl.persistence.entity.ResourceEntity">
  
  <!-- RESOURCE INSERT -->

  <insert id="insertResource" parameterType="org.activiti.engine.impl.persistence.entity.ResourceEntity">
    insert into ${prefix}ACT_GE_BYTEARRAY(ID_, REV_, NAME_, BYTES_, DEPLOYMENT_ID_, GENERATED_)
    values (#{id, jdbcType=VARCHAR}, 1, #{name, jdbcType=VARCHAR}, #{bytes, jdbcType=BLOB}, #{deploymentId, jdbcType=VARCHAR}, #{generated, jdbcType=BOOLEAN})  
  </insert>

  <insert id="bulkInsertResource" parameterType="java.util.List">
    INSERT INTO ${prefix}ACT_GE_BYTEARRAY(ID_, REV_, NAME_, BYTES_, DEPLOYMENT_ID_, GENERATED_) VALUES 
      <foreach collection="list" item="byteArr" index="index" separator=","> 
        (#{byteArr.id, jdbcType=VARCHAR},
         1,
         #{byteArr.name, jdbcType=VARCHAR},
         #{byteArr.bytes, jdbcType=BLOB},
         #{byteArr.deploymentId, jdbcType=VARCHAR},
         #{byteArr.generated, jdbcType=BOOLEAN})
      </foreach>
  </insert>

  <insert id="bulkInsertResource_oracle" parameterType="java.util.List">
    INSERT ALL
      <foreach collection="list" item="resource" index="index"> 
        INTO ${prefix}ACT_GE_BYTEARRAY(ID_, REV_, NAME_, BYTES_, DEPLOYMENT_ID_, GENERATED_) VALUES 
        (#{resource.id, jdbcType=VARCHAR},
         1,
         #{resource.name, jdbcType=VARCHAR},
         #{resource.bytes, jdbcType=BLOB},
         #{resource.deploymentId, jdbcType=VARCHAR},
         #{resource.generated, jdbcType=BOOLEAN})
      </foreach>
    SELECT * FROM dual
  </insert>
  
  <!-- RESOURCE UPDATE -->

  <!-- RESOURCE DELETE -->

  <delete id="deleteResourcesByDeploymentId" parameterType="string">
    delete from ${prefix}ACT_GE_BYTEARRAY where DEPLOYMENT_ID_ = #{id}
  </delete>
  
  <!-- RESOURCE RESULTMAP -->

  <resultMap id="resourceResultMap" type="org.activiti.engine.impl.persistence.entity.ResourceEntity">
    <id property="id" column="ID_" jdbcType="VARCHAR" />
    <result property="name" column="NAME_" jdbcType="VARCHAR"/>
    <result property="bytes" column="BYTES_" jdbcType="BLOB"/>
    <result property="generated" column="GENERATED_" jdbcType="BOOLEAN"/>
  </resultMap>
  
  <!-- RESOURCE SELECT -->

  <select id="selectResourceNamesByDeploymentId" parameterType="org.activiti.engine.impl.db.ListQueryParameterObject" resultType="string">
    select NAME_ from ${prefix}ACT_GE_BYTEARRAY where DEPLOYMENT_ID_ = #{parameter} order by NAME_ asc
  </select>
  
  <select id="selectResourceByDeploymentIdAndResourceName" parameterType="map" resultMap="resourceResultMap">
    select * from ${prefix}ACT_GE_BYTEARRAY 
    where DEPLOYMENT_ID_ = #{deploymentId}
          AND NAME_ = #{resourceName}
  </select>

  <select id="selectResourcesByDeploymentId" parameterType="org.activiti.engine.impl.db.ListQueryParameterObject" resultMap="resourceResultMap">
    select * from ${prefix}ACT_GE_BYTEARRAY where DEPLOYMENT_ID_ = #{parameter} order by NAME_ asc
  </select>  

  <!-- postgresql specific -->
  <resultMap id="resourceResultMap_postgres" type="org.activiti.engine.impl.persistence.entity.ResourceEntity">
    <id property="id" column="ID_" jdbcType="VARCHAR" />
    <result property="name" column="NAME_" jdbcType="VARCHAR"/>
    <result property="bytes" column="BYTES_" jdbcType="BINARY"/>
    <result property="generated" column="GENERATED_" jdbcType="BOOLEAN"/>
  </resultMap>
    
  <!-- postgresql specific -->
  <select id="selectResourceByDeploymentIdAndResourceName_postgres" parameterType="map" resultMap="resourceResultMap_postgres">
    select * from ${prefix}ACT_GE_BYTEARRAY 
    where DEPLOYMENT_ID_ = #{deploymentId}
          AND NAME_ = #{resourceName}
  </select>
  
  <!-- postgresql specific -->
  <select id="selectResourcesByDeploymentId_postgres" parameterType="org.activiti.engine.impl.db.ListQueryParameterObject" resultMap="resourceResultMap_postgres">
    select * from ${prefix}ACT_GE_BYTEARRAY where DEPLOYMENT_ID_ = #{parameter} order by NAME_ asc
  </select>  
  
</mapper>
