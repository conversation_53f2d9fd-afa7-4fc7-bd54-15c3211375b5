<?xml version="1.0" encoding="UTF-8" ?> 

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd"> 
  
<mapper namespace="org.activiti.engine.impl.persistence.entity.DeploymentEntity">

  <!-- DEPLOYMENT INSERT -->
  
  <insert id="insertDeployment" parameterType="org.activiti.engine.impl.persistence.entity.DeploymentEntity">
    insert into ${prefix}ACT_RE_DEPLOYMENT(ID_, NAME_, CATEGORY_, TENANT_ID_, DEPLOY_TIME_)
    values(#{id, jdbcType=VARCHAR}, #{name, jdbcType=VARCHAR}, #{category, jdbcType=VARCHAR}, #{tenantId, jdbcType=VARCHAR}, #{deploymentTime, jdbcType=TIMESTAMP})
  </insert>

  <insert id="bulkInsertDeployment" parameterType="java.util.List">
    insert into ${prefix}ACT_RE_DEPLOYMENT(ID_, NAME_, CATEGORY_, TENANT_ID_, DEPLOY_TIME_)
    values
      <foreach collection="list" item="deployment" index="index" separator=",">
        (#{deployment.id, jdbcType=VARCHAR},
         #{deployment.name, jdbcType=VARCHAR},
         #{deployment.category, jdbcType=VARCHAR},
         #{deployment.tenantId, jdbcType=VARCHAR},
         #{deployment.deploymentTime, jdbcType=TIMESTAMP})
      </foreach>
  </insert>

  <insert id="bulkInsertDeployment_oracle" parameterType="java.util.List">
    INSERT ALL 
      <foreach collection="list" item="deployment" index="index">
        into ${prefix}ACT_RE_DEPLOYMENT(ID_, NAME_, CATEGORY_, TENANT_ID_, DEPLOY_TIME_) VALUES 
          (#{deployment.id, jdbcType=VARCHAR},
           #{deployment.name, jdbcType=VARCHAR},
           #{deployment.category, jdbcType=VARCHAR},
           #{deployment.tenantId, jdbcType=VARCHAR},
           #{deployment.deploymentTime, jdbcType=TIMESTAMP})
      </foreach>
    SELECT * FROM dual
  </insert>

  <!-- DEPLOYMENT UPDATE -->
  
  <update id="updateDeployment" parameterType="org.activiti.engine.impl.persistence.entity.DeploymentEntity">
    update ${prefix}ACT_RE_DEPLOYMENT set
      CATEGORY_ = #{category, jdbcType=VARCHAR},
      TENANT_ID_ = #{tenantId, jdbcType=VARCHAR}
    where ID_ = #{id, jdbcType=VARCHAR}
  </update>
  
  <!-- DEPLOYMENT DELETE -->
  
  <delete id="deleteDeployment" parameterType="string">
    delete from ${prefix}ACT_RE_DEPLOYMENT where ID_ = #{id}
  </delete>

  <!-- DEPLOYMENT RESULTMAP -->
  
  <resultMap id="deploymentResultMap" type="org.activiti.engine.impl.persistence.entity.DeploymentEntity">
    <id property="id" column="ID_" jdbcType="VARCHAR" />
    <result property="name" column="NAME_" jdbcType="VARCHAR" />
    <result property="category" column="CATEGORY_" jdbcType="VARCHAR" />
    <result property="tenantId" column="TENANT_ID_" jdbcType="VARCHAR" />
    <result property="deploymentTime" column="DEPLOY_TIME_" jdbcType="TIMESTAMP"/>
  </resultMap>

  <!-- DEPLOYMENT SELECT -->

  <select id="selectDeploymentsByQueryCriteria" parameterType="org.activiti.engine.impl.DeploymentQueryImpl" resultMap="deploymentResultMap">
  	${limitBefore}
    select distinct RES.* ${limitBetween}
    <include refid="selectDeploymentsByQueryCriteriaSql"/>
    ${orderBy}
    ${limitAfter}
  </select>

  <select id="selectDeploymentCountByQueryCriteria" parameterType="org.activiti.engine.impl.DeploymentQueryImpl" resultType="long">
    select count(distinct RES.ID_)
    <include refid="selectDeploymentsByQueryCriteriaSql"/>
  </select>
  
  <sql id="selectDeploymentsByQueryCriteriaSql">  
    from ${prefix}ACT_RE_DEPLOYMENT RES
    <if test="processDefinitionKey != null or processDefinitionKeyLike != null">
    	inner join ${prefix}ACT_RE_PROCDEF PROCDEF on RES.ID_ = PROCDEF.DEPLOYMENT_ID_
    </if>
    <where>
      <if test="deploymentId != null">
        RES.ID_ = #{deploymentId}
      </if>
      <if test="name != null">
        and RES.NAME_ = #{name}
      </if>
      <if test="nameLike != null">
        and RES.NAME_ like #{nameLike}${wildcardEscapeClause}
      </if>
      <if test="category != null">
        and RES.CATEGORY_ = #{category}
      </if>
      <if test="categoryNotEquals != null">
        and ( RES.CATEGORY_ &lt;&gt; #{categoryNotEquals} OR RES.CATEGORY_ is null )  
      </if>
      <if test="tenantId != null">
        and RES.TENANT_ID_ = #{tenantId}
      </if>
      <if test="tenantIdLike != null">
        and RES.TENANT_ID_ like #{tenantIdLike}${wildcardEscapeClause}
      </if>
      <if test="withoutTenantId">
        and (RES.TENANT_ID_ = '' or RES.TENANT_ID_ is null)
      </if>
      <if test="processDefinitionKey != null">
        and PROCDEF.KEY_ = #{processDefinitionKey}
      </if>
      <if test="processDefinitionKeyLike != null">
        and PROCDEF.KEY_ like #{processDefinitionKeyLike}${wildcardEscapeClause}
      </if>
    </where>
  </sql>
    
  <select id="selectDeploymentsByName" parameterType="org.activiti.engine.impl.db.ListQueryParameterObject" resultMap="deploymentResultMap">
    select * from ${prefix}ACT_RE_DEPLOYMENT D where NAME_=#{parameter} order by D.DEPLOY_TIME_ desc
  </select>
  
  <select id="selectDeploymentById" parameterType="string" resultMap="deploymentResultMap">
    select * from ${prefix}ACT_RE_DEPLOYMENT where ID_ = #{id}
  </select>

  <!-- mysql specific -->
  <select id="selectDeploymentsByQueryCriteria_mysql" parameterType="org.activiti.engine.impl.DeploymentQueryImpl" resultMap="deploymentResultMap">
  	${limitBefore}
    select distinct RES.* ${limitBetween}
    <include refid="selectDeploymentsByQueryCriteriaSql"/>
   	${orderBy}
    ${limitAfter}
  </select>
  
  <!-- mysql specific -->
  <select id="selectDeploymentCountByQueryCriteria_mysql" parameterType="org.activiti.engine.impl.DeploymentQueryImpl" resultType="long">
    select distinct count(RES.ID_)
    <include refid="selectDeploymentsByQueryCriteriaSql"/>
  </select>

  <select id="selectDeploymentByNativeQuery" parameterType="java.util.Map" resultMap="deploymentResultMap">
    <if test="resultType == 'LIST_PAGE'">
      ${limitBefore}
    </if>
    ${sql}
    <if test="resultType == 'LIST_PAGE'">
      ${limitAfter}
    </if>
  </select>

  <select id="selectDeploymentByNativeQuery_mssql_or_db2" parameterType="java.util.Map" resultMap="deploymentResultMap">
    <if test="resultType == 'LIST_PAGE'">
      ${limitBeforeNativeQuery}
    </if>
    ${sql}
    <if test="resultType == 'LIST_PAGE'">
      ${limitAfter}
    </if>
  </select>

  <select id="selectDeploymentCountByNativeQuery" parameterType="java.util.Map" resultType="long">
    ${sql}
  </select>

</mapper>